<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة القوائم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1e1e1e;
            color: #e0e0e0;
            direction: rtl;
        }
        .test {
            margin: 20px 0;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .test h3 {
            color: #3b82f6;
            margin-top: 0;
        }
        .input {
            background: #333;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .output {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #555;
        }
        .debug {
            background: #2d1b69;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>تشخيص مشكلة القوائم</h1>
    
    <div class="test">
        <h3>اختبار 1: قائمة مرقمة بسيطة</h3>
        <div class="input">1. العنصر الأول
2. العنصر الثاني
3. العنصر الثالث</div>
        <div class="output" id="output1"></div>
        <div class="debug" id="debug1"></div>
    </div>

    <div class="test">
        <h3>اختبار 2: قائمة نقطية بسيطة</h3>
        <div class="input">- العنصر الأول
- العنصر الثاني
- العنصر الثالث</div>
        <div class="output" id="output2"></div>
        <div class="debug" id="debug2"></div>
    </div>

    <div class="test">
        <h3>اختبار 3: قائمة تنتهي بنص</h3>
        <div class="input">1. العنصر الأول
2. العنصر الثاني
3. العنصر الثالث
هذا نص بعد القائمة</div>
        <div class="output" id="output3"></div>
        <div class="debug" id="debug3"></div>
    </div>

    <div class="test">
        <h3>اختبار 4: قائمة بدون نص بعدها</h3>
        <div class="input">- النقطة الأولى
- النقطة الثانية
- النقطة الثالثة</div>
        <div class="output" id="output4"></div>
        <div class="debug" id="debug4"></div>
    </div>

    <script>
        // نسخة مبسطة من دالة renderMarkdown للاختبار
        function testRenderMarkdown(text) {
            let debugInfo = [];
            debugInfo.push("النص الأصلي:");
            debugInfo.push(JSON.stringify(text));
            
            // مرحلة 1: تحويل إلى علامات
            text = text.replace(/^(\d+)\.\s(.+)$/gm, function(match, num, content) {
                debugInfo.push(`تحويل قائمة مرقمة: ${match} -> @@LIST_OL_ITEM@@${content}@@LIST_OL_ITEM_END@@`);
                return `@@LIST_OL_ITEM@@${content}@@LIST_OL_ITEM_END@@`;
            });
            
            text = text.replace(/^[-*+]\s(.+)$/gm, function(match, content) {
                debugInfo.push(`تحويل قائمة نقطية: ${match} -> @@LIST_UL_ITEM@@${content}@@LIST_UL_ITEM_END@@`);
                return `@@LIST_UL_ITEM@@${content}@@LIST_UL_ITEM_END@@`;
            });
            
            debugInfo.push("بعد التحويل إلى علامات:");
            debugInfo.push(JSON.stringify(text));
            
            // مرحلة 2: تجميع القوائم المرقمة
            text = text.replace(/(@@LIST_OL_ITEM@@[\s\S]*?@@LIST_OL_ITEM_END@@(\s*@@LIST_OL_ITEM@@[\s\S]*?@@LIST_OL_ITEM_END@@)*)/g, function(match) {
                debugInfo.push(`تجميع قائمة مرقمة: ${match}`);
                const items = match.match(/@@LIST_OL_ITEM@@([\s\S]*?)@@LIST_OL_ITEM_END@@/g);
                if (!items) return match;
                
                const listItems = items.map(item => {
                    const content = item.replace(/@@LIST_OL_ITEM@@|@@LIST_OL_ITEM_END@@/g, '').trim();
                    return `<li>${content}</li>`;
                }).join('');
                const result = `<ol>${listItems}</ol>`;
                debugInfo.push(`نتيجة التجميع: ${result}`);
                return result;
            });
            
            // مرحلة 3: تجميع القوائم النقطية
            text = text.replace(/(@@LIST_UL_ITEM@@[\s\S]*?@@LIST_UL_ITEM_END@@(\s*@@LIST_UL_ITEM@@[\s\S]*?@@LIST_UL_ITEM_END@@)*)/g, function(match) {
                debugInfo.push(`تجميع قائمة نقطية: ${match}`);
                const items = match.match(/@@LIST_UL_ITEM@@([\s\S]*?)@@LIST_UL_ITEM_END@@/g);
                if (!items) return match;
                
                const listItems = items.map(item => {
                    const content = item.replace(/@@LIST_UL_ITEM@@|@@LIST_UL_ITEM_END@@/g, '').trim();
                    return `<li>${content}</li>`;
                }).join('');
                const result = `<ul>${listItems}</ul>`;
                debugInfo.push(`نتيجة التجميع: ${result}`);
                return result;
            });
            
            // مرحلة 4: تنظيف العناصر المتبقية
            text = text.replace(/@@LIST_[UO]L_ITEM@@([\s\S]*?)@@LIST_[UO]L_ITEM_END@@/g, function(_, content) {
                debugInfo.push(`تنظيف عنصر متبقي: ${content.trim()}`);
                return `<li>${content.trim()}</li>`;
            });
            
            debugInfo.push("النتيجة النهائية:");
            debugInfo.push(JSON.stringify(text));
            
            return { html: text, debug: debugInfo.join('\n') };
        }

        // تشغيل الاختبارات
        document.addEventListener('DOMContentLoaded', function() {
            const tests = [
                "1. العنصر الأول\n2. العنصر الثاني\n3. العنصر الثالث",
                "- العنصر الأول\n- العنصر الثاني\n- العنصر الثالث",
                "1. العنصر الأول\n2. العنصر الثاني\n3. العنصر الثالث\nهذا نص بعد القائمة",
                "- النقطة الأولى\n- النقطة الثانية\n- النقطة الثالثة"
            ];
            
            tests.forEach((test, index) => {
                const result = testRenderMarkdown(test);
                document.getElementById(`output${index + 1}`).innerHTML = result.html;
                document.getElementById(`debug${index + 1}`).textContent = result.debug;
            });
        });
    </script>
</body>
</html>
