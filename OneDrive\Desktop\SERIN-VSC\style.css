        /* أنماط عامة */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            direction: rtl !important;
            overflow: hidden !important;
        }

        body,
        .container,
        .chat-window,
        .sidebar,
        .file-explorer,
        .footer,
        .header {
            direction: inherit !important;
        }

        /* اجعل كل النصوص الافتراضية من اليمين لليسار */
        body,
        .container,
        .chat-window,
        .sidebar,
        .file-explorer,
        .footer,
        .header,
        .message,
        .think-container,
        .think-header,
        .think-content,
        .think-step,
        .message h1, .message h2, .message h3, .message h4, .message h5, .message h6,
        .message ul, .message ol,
        .message li,
        .message blockquote,
        .message p,
        .message div,
        .message span {
            direction: rtl !important;
            text-align: right !important;
        }

        /* الكود والجداول من اليسار لليمين */
        pre,
        code,
        .code-block,
        .code-header,
        .code-content,
        .message table,
        .message th,
        .message td,
        .mermaid-container,
        .mermaid-diagram {
            direction: ltr !important;
            text-align: left !important;
            unicode-bidi: embed;
        }



        :root {
            --primary-color: #3b82f6;
            --primary-color-light: #60a5fa;
            --primary-color-dark: #2563eb;
            --accent-color: #10b981;
            --bg-dark: #1e1e1e;
            --bg-darker: #181818;
            --bg-medium: #252525;
            --bg-light: #2a2a2a;
            --text-light: #e0e0e0;
            --text-medium: #a0a0a0;
            --text-dim: #6e6e6e;
            --border-color: #303030;
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
            --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
            --transition-fast: 0.15s ease;
            --transition-normal: 0.25s ease;
            --transition-slow: 0.35s ease;
            --status-bar-bg: #007acc;
            --status-bar-fg: #ffffff;
        }

        body,
        html {
            height: 100%;
            font-family: 'Inter', 'Tajawal', sans-serif;
            background-color: var(--bg-dark);
            color: var(--text-light);
            font-size: 14px;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-y: hidden !important;
            overflow-x: hidden !important;
        }

        .container {
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1280px;
            width: 100%;
            margin: 0 auto;
            align-items: center;
            transition: width var(--transition-normal), margin var(--transition-normal), all var(--transition-normal);
            overflow: hidden;
            /* منع ظهور شريط التمرير الأفقي والرأسي في الحاوية */
        }

        .container.explorer-visible {
            width: 100%;
            margin: 0 auto;
        }

        /* أنماط الرأس */
        .header {
            position: fixed;
            top: 0;
            max-width: 1280px;
            width: 800px;
            height: 48px;
            background: var(--bg-darker);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--border-color);
            left: 50%;
            transform: translateX(-50%);
            transition: all var(--transition-normal);
            padding: 0 16px;
            border-radius: 0 0 var(--radius-md) var(--radius-md);
        }

        .header.explorer-visible {
            transform: translateX(-50%);
        }

        .head {
            font-size: 1.1rem;
            padding: 6px 16px;
            border-radius: var(--radius-md);
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .header-buttons {
            display: flex;
            gap: 8px;
        }

        .header-button {
            width: 34px;
            height: 34px;
            border-radius: 6px;
            box-shadow: var(--shadow-sm);
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-medium);
            color: var(--text-medium);
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .header-button:hover {
            background: var(--primary-color);
            color: #fff;
            transform: translateY(-2px);
        }

        .header-button:active {
            transform: translateY(0) scale(0.95);
        }

        .header-button.active {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color-dark);
            box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
        }

        /* أنماط نافذة المحادثة */
        .chat-window {
            flex: 1;
            padding: 0 20px;
            margin: 58px auto 90px;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            gap: 16px;
            width: 800px;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) transparent;
            height: calc(100vh - 138px);
            /* ارتفاع الشاشة ناقص ارتفاع الهيدر والفوتر وهوامشهما */
        }

        .chat-window::-webkit-scrollbar {
            width: 6px;
        }

        .chat-window::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-window::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        /* شاشة الترحيب الأولية */
        .welcome-screen {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            opacity: 1;
            transition: all var(--transition-slow);
        }

        .welcome-screen.hidden {
            opacity: 0;
            pointer-events: none;
            transform: translateY(-20px);
        }

        .welcome-content {
            text-align: center;
            max-width: 600px;
            padding: 40px 20px;
        }

        .welcome-logo {
            margin-bottom: 40px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: var(--shadow-lg);
            animation: logoFloat 3s ease-in-out infinite;
        }

        .logo-icon i {
            font-size: 36px;
            color: white;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .logo-text {
            font-size: 48px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            letter-spacing: -1px;
        }

        .welcome-message {
            margin-bottom: 40px;
        }

        .welcome-message h2 {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-light);
            margin: 0 0 12px 0;
        }

        .welcome-message p {
            font-size: 16px;
            color: var(--text-medium);
            margin: 0;
            line-height: 1.6;
        }

        .welcome-suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 40px;
        }

        .suggestion-item {
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 20px;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 12px;
            text-align: right;
        }

        .suggestion-item:hover {
            background: var(--bg-light);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .suggestion-item i {
            font-size: 20px;
            color: var(--primary-color);
            flex-shrink: 0;
        }

        .suggestion-item span {
            color: var(--text-light);
            font-weight: 500;
            font-size: 14px;
        }

        .welcome-footer {
            margin-top: 20px;
        }

        .welcome-footer p {
            color: var(--text-medium);
            font-size: 14px;
            margin: 0;
        }

        /* تحسين للشاشات الصغيرة */
        @media (max-width: 768px) {
            .welcome-content {
                padding: 20px 16px;
            }

            .logo-icon {
                width: 60px;
                height: 60px;
            }

            .logo-icon i {
                font-size: 28px;
            }

            .logo-text {
                font-size: 36px;
            }

            .welcome-message h2 {
                font-size: 24px;
            }

            .welcome-suggestions {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .suggestion-item {
                padding: 16px;
            }
        }

        /* أنماط الرسائل */
        .message {
            max-width: 85%;
            padding: 12px 16px;
            margin: 4px 0;
            border-radius: var(--radius-md);
            line-height: 1.5;
            position: relative;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
            font-size: 1rem;
            word-wrap: break-word;
        }

        .message.user {
            background: var(--primary-color);
            margin-left: auto;
            margin-right: 8px;
            text-align: right;
            border-bottom-right-radius: 4px;
        }

        .message.bot {
            background: var(--bg-medium);
            margin-right: auto;
            margin-left: 8px;
            text-align: right;
            border-bottom-left-radius: 4px;
            border: 1px solid var(--border-color);
            direction: rtl;
        }

        .message.bot.error {
            background: rgba(220, 38, 38, 0.15);
            border: 1px solid rgba(220, 38, 38, 0.3);
        }

        .message-content {
            contain: content;
            overflow-wrap: break-word;
        }

        /* أنماط أزرار إجراءات الرسائل */
        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid var(--border-color);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .message.bot:hover .message-actions {
            opacity: 1;
        }

        .message-action-btn {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-dim);
            padding: 6px 12px;
            border-radius: var(--radius-sm);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .message-action-btn:hover {
            background: var(--bg-darker);
            color: var(--text-light);
            border-color: var(--primary-color);
        }

        .message-action-btn:active {
            transform: scale(0.95);
        }

        .message-action-btn.regenerating {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .message-action-btn.regenerating i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .message-action-btn.copied {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        /* مؤشر إعادة التوليد */
        .regenerating-indicator {
            display: inline-flex;
            align-items: center;
            padding: 10px 14px;
            background: var(--bg-medium);
            border-radius: var(--radius-md);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            font-size: 14px;
            gap: 8px;
        }

        .regenerating-indicator i {
            animation: spin 1s linear infinite;
        }

        /* أنماط أزرار التنقل بين الإجابات */
        .response-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 8px;
            padding: 8px 12px;
            background: var(--bg-light);
            border-radius: var(--radius-sm);
            border: 1px solid var(--border-color);
            font-size: 13px;
        }

        .navigation-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .response-counter {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 12px;
        }

        .navigation-buttons {
            display: flex;
            gap: 4px;
        }

        .nav-btn {
            background: transparent;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            color: var(--text-primary);
            cursor: pointer;
            padding: 6px 8px;
            font-size: 12px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 32px;
            height: 28px;
        }

        .nav-btn:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .nav-btn:active:not(:disabled) {
            transform: scale(0.95);
        }

        .nav-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: var(--bg-medium);
        }

        .nav-btn i {
            font-size: 10px;
        }

        /* تأثيرات الانتقال للرسائل */
        .message {
            transition: opacity 0.15s ease;
        }

        /* أنماط المؤشر الكتابي */
        .typing-indicator {
            display: inline-flex;
            align-items: center;
            padding: 10px 14px;
            background: var(--bg-medium);
            border-radius: var(--radius-md);
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            margin: 0 3px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        @keyframes typing {

            0%,
            60%,
            100% {
                transform: translateY(0);
                opacity: 0.6;
            }

            30% {
                transform: translateY(-5px);
                opacity: 1;
            }
        }

        /* منطقة الإدخال */
        .footer {
            position: fixed;
            bottom: 0;
            width: 800px;
            max-width: 1280px;
            background: var(--bg-darker);
            padding: 16px;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 12px;
            align-items: center;
            left: 50%;
            transform: translateX(-50%);
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-md);
            z-index: 900;
            border-radius: var(--radius-md) var(--radius-md) 0 0;
        }

        .footer.explorer-visible {
            transform: translateX(-50%);
        }

        .footer textarea {
            flex: 1;
            min-height: 40px;
            max-height: 150px;
            padding: 10px 16px;
            font-size: 0.95rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-medium);
            color: var(--text-light);
            resize: none;
            transition: all var(--transition-fast);
            font-family: inherit;
        }

        .footer textarea:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }

        .footer button {
            width: 42px;
            height: 42px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .footer button:hover {
            background: var(--primary-color-dark);
            transform: translateY(-2px);
        }

        .footer button:active {
            transform: translateY(0);
        }

        /* القائمة الجانبية - تصميم محسن */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            height: 100%;
            width: 280px;
            background: var(--bg-darker);
            color: white;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
            z-index: 1001;
            box-shadow: var(--shadow-lg);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        .sidebar.visible {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
            letter-spacing: 0.5px;
        }

        .new-chat-btn {
            width: 32px;
            height: 32px;
            background: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
        }

        .new-chat-btn:hover {
            background: var(--primary-color-dark);
            transform: scale(1.05);
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) rgba(0, 0, 0, 0.2);
        }

        .conversations-list::-webkit-scrollbar {
            width: 6px;
        }

        .conversations-list::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.2);
            border-radius: var(--radius-sm);
        }

        .conversations-list::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        .conversation-item {
            padding: 10px 12px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all var(--transition-fast);
            border-radius: var(--radius-md);
            margin: 4px 0;
            background: var(--bg-medium);
            border: 1px solid transparent;
            position: relative;
            min-height: 28px;
        }

        .conversation-item:hover {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
        }

        .conv-title {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: var(--text-light);
            font-size: 13px;
            padding-left: 35px; /* مساحة للأزرار في الجانب الأيسر */
        }

        .conversation-actions {
            position: absolute;
            left: 4px;
            top: 50%;
            transform: translateY(-50%);
            display: none;
            gap: 1px;
            background: var(--bg-medium);
            border-radius: 3px;
            padding: 1px 2px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            z-index: 5;
        }

        .conversation-item:hover .conversation-actions {
            display: flex;
        }

        .conversation-action {
            width: 16px;
            height: 16px;
            background: transparent;
            border: none;
            border-radius: 2px;
            color: var(--text-medium);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
            font-size: 10px;
        }

        .conversation-action:hover {
            background: var(--primary-color);
            color: #ffffff;
            transform: scale(1.05);
        }

        .conversation-action:active {
            transform: scale(0.9);
        }

        /* تحسين الألوان للأزرار المختلفة */
        .conversation-action:first-child:hover {
            background: #f44336 !important; /* أحمر للحذف */
        }

        .conversation-action:last-child:hover {
            background: #4caf50 !important; /* أخضر للمشاركة */
        }

        /* تحسينات أزرار المحادثات للأجهزة اللمسية */
        @media (hover: none) and (pointer: coarse) {
            .conversation-actions {
                display: flex !important; /* إظهار الأزرار دائماً على الأجهزة اللمسية */
                background: var(--bg-medium);
                border-radius: 3px;
                padding: 1px 2px;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--border-color);
                left: 6px;
            }

            .conversation-action {
                width: 18px;
                height: 18px;
                font-size: 10px;
                border-radius: 2px;
                margin: 0 1px;
                touch-action: manipulation;
            }

            .conv-title {
                padding-left: 45px !important; /* مساحة إضافية للأزرار */
            }

            .conversation-action:active {
                transform: scale(0.85);
                background: var(--primary-color) !important;
                color: #ffffff !important;
            }
        }

        /* مستكشف الملفات - نمط VS Code */
        .file-explorer {
            position: fixed;
            right: 0;
            left: auto;
            top: 0;
            height: 100%;
            width: 250px;
            background: var(--bg-medium);
            color: var(--text-medium);
            z-index: 1001;
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
            box-shadow: none;
            transform: translateX(100%);
        }

        .file-explorer.visible {
            right: 0;
            transform: translateX(0);
            box-shadow: var(--shadow-lg);
        }

        .explorer-header {
            padding: 0 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 35px;
            border-bottom: 1px solid var(--border-color);
            user-select: none;
            -webkit-user-select: none;
        }

        .explorer-title {
            font-size: 11px;
            font-weight: 600;
            color: var(--text-medium);
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .explorer-action {
            width: 22px;
            height: 22px;
            background: transparent;
            border: none;
            border-radius: 3px;
            color: var(--text-medium);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            opacity: 0.7;
        }

        .explorer-action:hover {
            background: var(--bg-light);
            opacity: 1;
        }

        .explorer-content {
            flex: 1;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--bg-light) var(--bg-medium);
        }

        .explorer-content::-webkit-scrollbar {
            width: 8px;
        }

        .explorer-content::-webkit-scrollbar-track {
            background: var(--bg-medium);
        }

        .explorer-content::-webkit-scrollbar-thumb {
            background-color: var(--bg-light);
            border-radius: 3px;
        }

        /* قسم المجلدات والملفات في VS Code */
        .explorer-item {
            position: relative;
            display: flex;
            align-items: center;
            min-height: 22px;
            padding: 0;
            margin: 0;
            border-radius: 0;
            transition: background 0.1s ease;
            border: none;
            cursor: pointer;
            color: var(--text-medium);
        }

        .explorer-item-content {
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            padding: 0 8px;
            height: 22px;
        }

        .explorer-item-icon {
            width: 16px;
            margin-right: 4px;
            text-align: center;
            flex-shrink: 0;
        }

        .explorer-item.file .explorer-item-icon {
            color: #75beff;
        }

        .explorer-item.folder .explorer-item-icon {
            color: #e3a700;
        }

        .explorer-item:hover {
            background: var(--bg-light);
        }

        .explorer-item.active {
            background: rgba(59, 130, 246, 0.2);
        }

        .explorer-item-actions {
            position: absolute;
            left: 4px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 5;
            display: none;
            align-items: center;
            gap: 1px;
            background: var(--bg-medium);
            border-radius: 3px;
            padding: 1px 2px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .explorer-item:hover .explorer-item-actions {
            display: flex;
        }

        .explorer-item-action {
            width: 16px;
            height: 16px;
            background: transparent;
            border: none;
            border-radius: 2px;
            color: var(--text-medium);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.15s ease;
            position: relative;
        }

        .explorer-item-action:hover {
            background: var(--primary-color);
            color: #ffffff;
            transform: scale(1.05);
        }

        .explorer-item-action:active {
            transform: scale(0.9);
        }

        /* تحسينات للأجهزة اللمسية */
        @media (hover: none) and (pointer: coarse) {
            .explorer-item-actions {
                display: flex !important; /* إظهار الأزرار دائماً على الأجهزة اللمسية */
                background: var(--bg-medium);
                border-radius: 3px;
                padding: 1px 2px;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--border-color);
                left: 6px;
            }

            .explorer-item-action {
                width: 18px;
                height: 18px;
                font-size: 10px;
                border-radius: 2px;
                margin: 0 1px;
                touch-action: manipulation; /* تحسين الاستجابة للمس */
            }

            .explorer-item-content {
                padding-left: 50px !important; /* مساحة إضافية للأزرار */
            }

            /* تحسين تأثيرات الأزرار على الأجهزة اللمسية */
            .explorer-item-action:active {
                transform: scale(0.85);
                background: var(--primary-color) !important;
                color: #ffffff !important;
            }
        }

        /* تحسينات عامة لجميع الأجهزة */
        .explorer-item {
            position: relative;
            min-height: 28px; /* ضمان ارتفاع كافي للأزرار */
        }

        .explorer-item-content {
            padding-left: 40px; /* مساحة افتراضية للأزرار في الجانب الأيسر */
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .explorer-item-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* تحسين الألوان والتأثيرات */
        .explorer-item-action.delete-action:hover {
            background: #f44336 !important;
            color: #ffffff !important;
        }

        .explorer-item-action.edit-action:hover {
            background: #2196f3 !important;
            color: #ffffff !important;
        }

        /* VS Code Section Headers */
        .explorer-section-header {
            padding: 0 16px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--text-dim);
            background: var(--bg-medium);
            border-bottom: 1px solid var(--border-color);
            user-select: none;
            -webkit-user-select: none;
        }

        .explorer-section-actions {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .explorer-section-action {
            width: 16px;
            height: 16px;
            background: transparent;
            border: none;
            border-radius: 2px;
            color: var(--text-dim);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.1s ease;
        }

        .explorer-section-action:hover {
            color: var(--text-light);
        }

        /* Breadcrumb Navigation */
        .breadcrumb {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            background: var(--bg-darker);
            overflow-x: auto;
            white-space: nowrap;
            border-bottom: 1px solid var(--border-color);
            scrollbar-width: none;
        }

        .breadcrumb::-webkit-scrollbar {
            display: none;
        }

        .breadcrumb-item {
            color: var(--text-medium);
            cursor: pointer;
            padding: 0 4px;
            font-size: 12px;
        }

        .breadcrumb-item:hover {
            color: var(--text-light);
        }

        .breadcrumb-separator {
            color: var(--text-dim);
            margin: 0 2px;
        }

        /* واجهة تشغيل الأكواد - VS Code style */
        .code-executor {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-dark);
            z-index: 2000;
            display: flex;
            flex-direction: column;
            transform: translateY(100%);
            transition: transform 0.25s cubic-bezier(0.1, 0.9, 0.2, 1);
            box-shadow: var(--shadow-lg);
        }

        .code-executor.visible {
            transform: translateY(0);
        }

        .code-executor.explorer-visible {
            width: 100% !important;
            margin: 0 auto;
        }

        .executor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-medium);
            padding: 0;
            height: 35px;
            border-bottom: 1px solid var(--border-color);
            user-select: none;
            -webkit-user-select: none;
        }

        .file-tabs {
            display: flex;
            overflow-x: auto;
            flex-grow: 1;
            height: 100%;
            gap: 0;
            scrollbar-width: thin;
            scrollbar-color: var(--bg-light) transparent;
        }

        .file-tabs::-webkit-scrollbar {
            height: 3px;
        }

        .file-tabs::-webkit-scrollbar-track {
            background: transparent;
        }

        .file-tabs::-webkit-scrollbar-thumb {
            background-color: var(--bg-light);
            border-radius: 3px;
        }

        .file-tab {
            padding: 0 10px;
            display: flex;
            align-items: center;
            background: var(--bg-medium);
            color: var(--text-medium);
            cursor: pointer;
            font-size: 13px;
            min-width: 100px;
            max-width: 160px;
            position: relative;
            height: 100%;
            transition: background-color 0.1s ease;
            border-right: 1px solid var(--border-color);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .file-tab.active {
            background: var(--bg-dark);
            color: var(--text-light);
            border-bottom: 1px solid var(--primary-color);
        }

        .file-tab:hover {
            background: var(--bg-light);
        }

        .file-tab-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            -webkit-user-select: none;
            user-select: none;
        }

        .file-tab-close {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            color: var(--text-dim);
            font-size: 14px;
            opacity: 0.7;
            transition: all 0.2s ease;
        }

        .file-tab:hover .file-tab-close {
            opacity: 1;
        }

        .file-tab-close:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .executor-actions {
            display: flex;
            height: 100%;
        }

        .executor-btn {
            width: 34px;
            height: 35px;
            background: transparent;
            border: none;
            color: var(--text-medium);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            position: relative;
            border-left: 1px solid var(--border-color);
        }

        .executor-btn:hover {
            color: white;
            background: var(--bg-light);
        }

        .executor-btn.executor-run {
            color: #4caf50;
        }

        .executor-btn.executor-run:hover {
            background: rgba(76, 175, 80, 0.2);
        }

        .editor-container {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .editor-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
            z-index: 10;
        }

        .monaco-editor {
            width: 100% !important;
            height: 100% !important;
        }

        /* تحسينات التيرمنال - VS Code style */
        .executor-footer {
            height: 180px;
            background: var(--bg-darker);
            border-top: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 10;
        }

        .executor-footer.collapsed {
            height: 32px;
        }

        .executor-footer.hidden {
            height: 0;
            overflow: hidden;
            border-top: none;
        }

        .executor-footer.maximized {
            height: 50vh;
        }

        .terminal-header {
            padding: 0;
            background: var(--bg-medium);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-medium);
            user-select: none;
            -webkit-user-select: none;
            height: 32px;
            border-bottom: 1px solid var(--border-color);
        }

        .terminal-tabs {
            display: flex;
            height: 100%;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .terminal-tabs::-webkit-scrollbar {
            display: none;
        }

        .terminal-tab {
            padding: 0 12px;
            display: flex;
            align-items: center;
            height: 100%;
            border-right: 1px solid var(--border-color);
            color: var(--text-medium);
            background: var(--bg-darker);
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .terminal-tab.active {
            color: var(--text-light);
            background: var(--bg-darker);
            border-top: 2px solid var(--primary-color);
            font-weight: 500;
        }

        .terminal-tab:hover:not(.active) {
            background: var(--bg-light);
        }

        .terminal-actions {
            display: flex;
            height: 100%;
        }

        .terminal-header button {
            background: transparent;
            border: none;
            color: var(--text-medium);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            border-left: 1px solid var(--border-color);
        }

        .terminal-header button:hover {
            color: white;
            background: var(--bg-light);
        }

        .terminal {
            flex: 1;
            padding: 10px 15px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            color: #e0e0e0;
            background: var(--bg-darker);
            white-space: pre-wrap;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: var(--bg-light) transparent;
            line-height: 1.5;
        }

        .terminal::-webkit-scrollbar {
            width: 8px;
        }

        .terminal::-webkit-scrollbar-track {
            background: transparent;
        }

        .terminal::-webkit-scrollbar-thumb {
            background-color: var(--bg-light);
            border-radius: 3px;
        }

        /* رسائل التيرمنال */
        .terminal-message {
            padding: 4px 0;
            color: var(--text-medium);
            font-style: italic;
        }

        .terminal-message.error {
            color: #f44336;
        }

        .terminal-message.success {
            color: #4caf50;
        }

        .terminal-message.warning {
            color: #ff9800;
        }

        /* نظام الإشعارات التفاعلية المتناسق مع التصميم */
        .notification-container {
            position: fixed;
            top: 24px;
            right: 24px;
            z-index: 10000;
            pointer-events: none;
            max-width: 400px;
            min-width: 300px;
            max-height: calc(100vh - 48px);
            overflow-y: auto;
            overflow-x: hidden;
            padding: 0 4px;
            font-family: 'Inter', 'Tajawal', sans-serif;
        }

        /* تحسين للشاشات الصغيرة */
        @media (max-width: 768px) {
            .notification-container {
                top: 16px;
                right: 16px;
                left: 16px;
                max-width: none;
                min-width: auto;
            }
        }

        /* تحسين للشاشات الكبيرة */
        @media (min-width: 1400px) {
            .notification-container {
                max-width: 480px;
                top: 32px;
                right: 32px;
            }
        }

        .notification-container::-webkit-scrollbar {
            width: 4px;
        }

        .notification-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .notification-container::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 2px;
        }

        /* مواضع مختلفة للإشعارات */
        .notification-top-left {
            top: 24px;
            left: 24px;
            right: auto;
        }

        .notification-bottom-right {
            top: auto;
            bottom: 24px;
            right: 24px;
        }

        .notification-bottom-left {
            top: auto;
            bottom: 24px;
            left: 24px;
            right: auto;
        }

        .notification-top-center {
            top: 24px;
            left: 50%;
            right: auto;
            transform: translateX(-50%);
        }

        .notification-bottom-center {
            top: auto;
            bottom: 24px;
            left: 50%;
            right: auto;
            transform: translateX(-50%);
        }

        /* تحسين للمواضع المختلفة على الشاشات الصغيرة */
        @media (max-width: 768px) {
            .notification-top-center,
            .notification-bottom-center {
                left: 16px;
                right: 16px;
                transform: none;
                max-width: none;
            }

            .notification-top-left,
            .notification-bottom-left {
                left: 16px;
            }
        }

        .notification {
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 16px 18px;
            margin-bottom: 12px;
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(8px);
            pointer-events: auto;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            direction: rtl;
            cursor: pointer;
            min-height: 56px;
            border-left: 3px solid var(--primary-color);
        }

        .notification:hover {
            transform: translateX(-5px) scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.show:hover {
            transform: translateX(-5px) scale(1.02);
        }

        .notification.hide {
            transform: translateX(100%);
            opacity: 0;
            margin-bottom: 0;
            padding-top: 0;
            padding-bottom: 0;
            max-height: 0;
        }

        .notification.success {
            border-left-color: var(--accent-color);
            background: var(--bg-medium);
        }

        .notification.error {
            border-left-color: #f44336;
            background: var(--bg-medium);
        }

        .notification.warning {
            border-left-color: #ff9800;
            background: var(--bg-medium);
        }

        .notification.info {
            border-left-color: var(--primary-color);
            background: var(--bg-medium);
        }

        .notification-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .notification-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--text-light);
            font-size: 14px;
            line-height: 1.4;
        }

        .notification-icon {
            width: 20px;
            height: 20px;
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            flex-shrink: 0;
            box-shadow: var(--shadow-sm);
        }

        .notification.success .notification-icon {
            background: var(--accent-color);
        }

        .notification.error .notification-icon {
            background: #f44336;
        }

        .notification.warning .notification-icon {
            background: #ff9800;
        }

        .notification.info .notification-icon {
            background: var(--primary-color);
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--text-medium);
            cursor: pointer;
            padding: 4px;
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
            font-size: 14px;
            line-height: 1;
        }

        .notification-close:hover {
            background: var(--bg-light);
            color: var(--text-light);
        }

        .notification-content {
            color: var(--text-medium);
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 12px;
            margin-top: 6px;
        }

        .notification-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .notification-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: var(--bg-darker);
            color: var(--text-light);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 12px;
            transition: all var(--transition-fast);
        }

        .notification-btn:hover {
            background: var(--bg-light);
        }

        .notification-btn.primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .notification-btn.primary:hover {
            background: var(--primary-color-dark);
        }

        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            background: var(--primary-color);
            transition: width 0.1s linear;
        }

        /* Modal Dialog المتناسق مع التصميم */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(6px);
            z-index: 15000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Inter', 'Tajawal', sans-serif;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 24px;
            max-width: 480px;
            min-width: 360px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
            transform: scale(0.9) translateY(20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            direction: rtl;
        }

        /* تحسين للشاشات الصغيرة */
        @media (max-width: 768px) {
            .modal {
                min-width: auto;
                width: 95%;
                padding: 20px;
                border-radius: 16px;
            }
        }

        .modal-overlay.show .modal {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
            line-height: 1.3;
        }

        .modal-icon {
            width: 28px;
            height: 28px;
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            flex-shrink: 0;
            box-shadow: var(--shadow-sm);
        }

        .modal.confirm .modal-icon {
            background: #ff9800;
        }

        .modal.success .modal-icon {
            background: var(--accent-color);
        }

        .modal.error .modal-icon {
            background: #f44336;
        }

        .modal.info .modal-icon {
            background: var(--primary-color);
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-medium);
            cursor: pointer;
            padding: 6px;
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
            font-size: 16px;
            line-height: 1;
        }

        .modal-close:hover {
            background: var(--bg-light);
            color: var(--text-light);
        }

        .modal-content {
            color: var(--text-medium);
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
            margin-top: 12px;
        }

        .modal-actions {
            display: flex;
            gap: 14px;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 10px 20px;
            border: 1px solid var(--border-color);
            background: var(--bg-darker);
            color: var(--text-light);
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all var(--transition-fast);
            min-width: 80px;
            position: relative;
            overflow: hidden;
        }

        .modal-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .modal-btn:hover::before {
            left: 100%;
        }

        .modal-btn:hover {
            background: var(--bg-light);
        }

        .modal-btn.primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .modal-btn.primary:hover {
            background: var(--primary-color-dark);
        }

        .modal-btn.danger {
            background: #f44336;
            border-color: #f44336;
            color: white;
        }

        .modal-btn.danger:hover {
            background: #d32f2f;
        }

        .terminal-welcome {
            color: #9e9e9e;
            font-style: italic;
            padding: 4px 0;
        }

        /* زر التيرمنال في شريط الحالة */
        .terminal-toggle-btn {
            color: #e0e0e0;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 0 10px;
            height: 100%;
            transition: background-color 0.2s;
            white-space: nowrap;
        }

        .terminal-toggle-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .terminal-toggle-btn i {
            font-size: 12px;
            flex-shrink: 0;
        }

        /* تحسين تجربة المستخدم للتيرمنال */
        .executor-footer.collapsed .terminal {
            display: none;
        }

        .executor-footer.collapsed .terminal-header {
            border-bottom: none;
        }

        /* تعديل حجم التيرمنال - مقبض السحب */
        .terminal-resizer {
            height: 4px;
            background: var(--bg-medium);
            cursor: row-resize;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            transform: translateY(-2px);
            opacity: 0;
            transition: opacity 0.2s;
        }

        .executor-footer:hover .terminal-resizer {
            opacity: 1;
        }

        /* تحسين مظهر شريط الحالة - متجاوب مع الأجهزة */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--status-bar-bg);
            color: var(--status-bar-fg);
            font-size: 12px;
            height: 22px;
            padding: 0 8px;
            border-top: 1px solid var(--border-color);
            -webkit-user-select: none;
            user-select: none;
            overflow: hidden;
        }

        .status-items-left, .status-items-right {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .status-item {
            display: flex;
            align-items: center;
            padding: 0 8px;
            height: 100%;
            cursor: pointer;
            white-space: nowrap;
            gap: 4px;
            transition: background-color 0.2s;
        }

        .status-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .status-item i {
            font-size: 12px;
            margin-right: 4px;
        }

        .status-item-text {
            display: inline-block;
        }

        .cursor-position {
            min-width: 80px;
        }

        .language-indicator {
            min-width: 60px;
        }

        /* Responsive status bar */
        @media (max-width: 768px) {
            .status-bar {
                padding: 0 4px;
        }

            .status-item {
                padding: 0 4px;
        }

            .hide-on-small {
                display: none;
        }
        }

        @media (max-width: 480px) {
            .hide-on-tiny {
                display: none;
        }

            .status-item {
                padding: 0 2px;
        }

            /* Make touch targets bigger on small screens */
        .status-bar {
                height: 26px;
            }
        }

        /* Touch device improvements */
        @media (pointer: coarse) {
        .status-item {
                padding: 0 10px;
        }

            .status-bar {
                height: 28px;
            }
        }

        /* تحسينات الكود */
        .code-block {
            background: var(--bg-darker);
            border-radius: var(--radius-md);
            overflow: hidden;
            margin: 12px 0;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .code-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: var(--bg-medium);
            font-size: 0.85rem;
        }

        .language-label {
            color: var(--text-medium);
            font-family: monospace;
            text-transform: uppercase;
            font-size: 12px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .copy-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .copy-button:hover {
            background: var(--primary-color-dark);
        }

        .run-code-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            cursor: pointer;
            transition: all var(--transition-fast);
            margin-left: 6px;
        }

        .run-code-btn:hover {
            background: #059669;
        }

        .run-code-btn i {
            margin-right: 4px;
        }

        .code-content {
            padding: 10px;
            overflow-x: auto;
            background: var(--bg-darker);
        }

        pre[class*="language-"] {
            margin: 0;
            border-radius: 0;
        }

        /* تحسينات الجداول */
        .message table {
            width: 100%;
            margin: 12px 0;
            border-collapse: collapse;
            background: var(--bg-darker);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .message th,
        .message td {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            text-align: right;
        }

        .message th {
            background: var(--bg-medium);
            color: var(--primary-color);
            font-weight: 600;
        }

        /* تحسينات عملية التفكير */
        .think-container {
            background: var(--bg-darker);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            margin: 12px 0;
            overflow: hidden;

        }

        .think-header {
            padding: 10px 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            background: var(--bg-medium);
            transition: background-color var(--transition-fast);

        }



        .think-header:hover {
            background: var(--bg-light);
        }

        .think-toggle {
            transition: transform var(--transition-normal);
            color: var(--primary-color);
        }

        .think-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--primary-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .think-content {
            padding: 12px;
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--text-medium);
            border-top: 1px solid var(--border-color);
        }

        .think-step {
            position: relative;
            padding-left: 20px;
            margin: 8px 0;
        }

        .think-step::before {
            content: '▹';
            position: absolute;
            left: 0;
            color: var(--primary-color);
        }

        .think-divider {
            height: 1px;
            background: var(--border-color);
            margin: 12px 0;
        }

        /* Custom styles for inline elements */
        .message p {
            margin: 10px 0;
        }

        .message h1,
        .message h2,
        .message h3,
        .message h4,
        .message h5,
        .message h6 {
            margin-top: 16px;
            margin-bottom: 10px;
            font-weight: 600;
            line-height: 1.3;
        }

        .message h1 {
            font-size: 1.4rem;
            color: var(--primary-color);
        }

        .message h2 {
            font-size: 1.2rem;
            color: var(--text-light);
        }

        .message h3 {
            font-size: 1.1rem;
        }

        /* أنماط القوائم البسيطة والواضحة */
        .message ul,
        .message ol {
            margin: 10px 0;
            padding-right: 20px;
            padding-left: 0;
            line-height: 1.5;
            clear: both;
        }

        .message li {
            margin: 4px 0;
            padding: 0;
            line-height: 1.5;
        }

        /* القوائم المتداخلة */
        .message li ul,
        .message li ol {
            margin: 6px 0;
            padding-right: 18px;
        }

        /* تحسين المحتوى المختلط في القوائم */
        .message li p {
            margin: 4px 0;
            display: inline-block;
            width: 100%;
        }

        .message li h1,
        .message li h2,
        .message li h3,
        .message li h4,
        .message li h5,
        .message li h6 {
            margin: 6px 0 2px 0;
            font-size: inherit;
        }

        .message li blockquote {
            margin: 6px 0;
            padding: 4px 8px;
        }

        .message li pre,
        .message li code {
            margin: 4px 0;
            display: block;
        }

        /* تحسين القوائم في الأجهزة الصغيرة */
        @media (max-width: 768px) {
            .message ul,
            .message ol {
                padding-right: 16px;
            }

            .message li ul,
            .message li ol {
                padding-right: 14px;
            }
        }



        .message a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all var(--transition-fast);
        }

        .message a:hover {
            text-decoration: underline;
        }

        .message blockquote {
            border-right: 3px solid var(--primary-color);
            margin: 12px 0;
            padding: 4px 12px;
            background: var(--bg-darker);
            border-radius: var(--radius-sm);
        }



        /* استثناء للكود والعناصر التقنية */
        .message pre,
        .message code,
        .message .code-block,
        .message .code-header,
        .message .code-content,
        .message table,
        .message th,
        .message td {
            direction: ltr !important;
            text-align: left !important;
        }

        .message img {
            max-width: 100%;
            border-radius: var(--radius-md);
            margin: 12px 0;
        }

        .message hr {
            border: none;
            height: 1px;
            background: var(--border-color);
            margin: 16px 0;
        }

        code.inline-code {
            background: var(--bg-darker);
            padding: 2px 5px;
            border-radius: var(--radius-sm);
            font-family: "Consolas", monospace;
            font-size: 0.9em;
            color: #e9c46a;
        }

        /* تحسينات للأجهزة المتوسطة (التابلت) */
        @media (max-width: 1024px) and (min-width: 769px) {
            .explorer-item-actions {
                display: flex !important; /* إظهار الأزرار دائماً على التابلت */
                background: var(--bg-medium);
                border-radius: 3px;
                padding: 1px 2px;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
                border: 1px solid var(--border-color);
                left: 5px;
            }

            .explorer-item-action {
                width: 16px;
                height: 16px;
                font-size: 10px;
                border-radius: 2px;
                margin: 0 1px;
            }

            .explorer-item-content {
                padding-left: 45px !important; /* مساحة إضافية للأزرار */
            }

            /* تحسينات أزرار المحادثات للتابلت */
            .conversation-actions {
                display: flex !important;
                background: var(--bg-medium);
                border-radius: 3px;
                padding: 1px 2px;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
                border: 1px solid var(--border-color);
                left: 5px;
            }

            .conversation-action {
                width: 16px;
                height: 16px;
                font-size: 10px;
                border-radius: 2px;
                margin: 0 1px;
            }

            .conv-title {
                padding-left: 40px !important;
            }
        }

        /* تحسينات للجوال والأجهزة الصغيرة */
        @media (max-width: 992px) {

            .container.explorer-visible,
            .code-executor.explorer-visible {
                width: 100% !important;
                margin: 0 auto !important;
            }

            .file-explorer {
                width: 250px;
                right: -250px;
                transform: translateX(0);
            }

            .file-explorer.visible {
                right: 0;
                transform: translateX(0);
            }

            .header,
            .footer {
                left: 0;
                width: 100%;
                transform: none;
                border-radius: 0;
            }

            .header.explorer-visible,
            .footer.explorer-visible {
                left: 0;
                transform: none;
                width: 100%;
            }

            .chat-window {
                width: 100%;
                margin: 58px 0 90px;
                height: calc(100vh - 148px);
            }

            .code-executor {
                left: 0;
                width: 100%;
            }

            .sidebar,
            .pluginbar {
                width: 280px;
            }
        }

        @media (max-width: 820px) {

            .header,
            .footer {
                width: 100%;
                left: 0;
                transform: none;
            }

            .header.explorer-visible,
            .footer.explorer-visible {
                left: 0;
                transform: none;
            }

            .chat-window {
                width: 100%;
                margin: 48px 0 80px;
                height: calc(100vh - 128px);
            }
        }

        @media (max-width: 600px) {
            .message {
                max-width: 95%;
                margin-left: 8px;
                margin-right: 8px;
                font-size: 14px;
            }

            .message-actions {
                flex-wrap: wrap;
                gap: 6px;
            }

            .message-action-btn {
                padding: 4px 8px;
                font-size: 11px;
                flex: 1;
                min-width: 60px;
            }

            .message.bot:hover .message-actions,
            .message.bot .message-actions {
                opacity: 1; /* دائماً مرئية على الأجهزة المحمولة */
            }

            /* أنماط أزرار التنقل للأجهزة المحمولة */
            .response-navigation {
                flex-direction: column;
                gap: 8px;
                padding: 10px;
            }

            .navigation-info {
                justify-content: center;
            }

            .response-counter {
                font-size: 13px;
            }

            .navigation-buttons {
                justify-content: center;
                gap: 8px;
            }

            .nav-btn {
                min-width: 40px;
                height: 32px;
                padding: 8px 10px;
            }

            .nav-btn i {
                font-size: 12px;
            }
        }

            .head {
                font-size: 1rem;
                padding: 6px 12px;
            }

            .footer {
                padding: 10px;
            }

            .footer textarea {
                font-size: 14px;
                padding: 8px;
            }

            .footer button {
                width: 36px;
                height: 36px;
            }

            .chat-window {
                margin: 48px 0 70px;
                padding: 0 10px;
                height: calc(100vh - 118px);
            }

            .explorer-item-actions {
                position: absolute;
                left: 4px;
                top: 50%;
                transform: translateY(-50%);
                display: flex !important; /* إظهار الأزرار بشكل دائم على الأجهزة الصغيرة */
                background: var(--bg-medium);
                border-radius: 3px;
                padding: 1px 2px;
                box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);
                border: 1px solid var(--border-color);
            }

            .explorer-item-action {
                width: 18px !important;
                height: 18px !important;
                font-size: 10px !important;
                border-radius: 2px !important;
                margin: 0 1px;
            }

            .explorer-item-action:hover {
                transform: scale(1.05);
            }

            /* تحسين المسافة لتجنب التداخل */
            .explorer-item-content {
                padding-left: 50px !important; /* مساحة إضافية للأزرار */
            }

            /* تحسينات أزرار المحادثات للأجهزة الصغيرة */
            .conversation-actions {
                display: flex !important;
                background: var(--bg-medium);
                border-radius: 3px;
                padding: 1px 2px;
                box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);
                border: 1px solid var(--border-color);
                left: 4px;
            }

            .conversation-action {
                width: 18px !important;
                height: 18px !important;
                font-size: 10px !important;
                border-radius: 2px !important;
                margin: 0 1px;
            }

            .conv-title {
                padding-left: 45px !important;
            }
        

        /* اجبار محرر الأكواد على LTR دائمًا */
        .monaco-editor,
        .monaco-editor-background,
        .monaco-editor .view-lines {
            direction: ltr !important;
            text-align: left !important;
        }

        /* الأشرطة الجانبية الإضافية */
        .pluginbar {
            position: fixed;
            top: 0;
            height: 100%;
            background: var(--bg-darker);
            color: var(--text-light);
            z-index: 1100;
            box-shadow: var(--shadow-lg);
            border-color: var(--border-color);
            display: flex;
            flex-direction: column;
            transition: transform var(--transition-normal);
            width: 280px;
            left: 0;
            border-right: 1px solid var(--border-color);
            transform: translateX(-100%);
        }

        .pluginbar.visible {
            transform: translateX(0);
        }

        .pluginbar-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pluginbar-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
        }

        .pluginbar-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
            scrollbar-width: thin;
            scrollbar-color: var(--bg-light) var(--bg-darker);
        }

        .pluginbar-list::-webkit-scrollbar {
            width: 6px;
        }

        .pluginbar-list::-webkit-scrollbar-track {
            background: var(--bg-darker);
        }

        .pluginbar-list::-webkit-scrollbar-thumb {
            background-color: var(--bg-light);
            border-radius: 3px;
        }

        .plugin-item {
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            gap: 4px;
            background: var(--bg-medium);
            margin-bottom: 6px;
            border-radius: var(--radius-sm);
        }

        .plugin-name {
            font-size: 14px;
            color: var(--primary-color);
            font-weight: 500;
        }

        .plugin-desc {
            font-size: 13px;
            color: var(--text-medium);
        }

        .plugin-switch {
            margin-top: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plugin-switch input[type="checkbox"] {
            accent-color: var(--primary-color);
        }



        /* أنيميشن خفيف للزر عند فتح/إغلاق المستكشف */
        #explorer-toggle {
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        #explorer-toggle.active i {
            transform: rotate(360deg);
            transition: transform 0.5s ease;
        }

        /* منع ظهور شريط التمرير الأفقي على الشاشات الصغيرة */
        @media (max-width: 850px) {

            .header,
            .footer {
                width: 100%;
                max-width: 100%;
                border-radius: 0;
            }

            .chat-window {
                width: 100%;
                max-width: 100%;
                padding: 0 10px;
            }

            .container,
            html,
            body {
                overflow-x: hidden !important;
                width: 100%;
                max-width: 100%;
            }
        }

        /* ضمان عدم ظهور شريط التمرير على الأجهزة المحمولة */
        @media (max-width: 480px) {
            .message {
                max-width: 95%;
            }

            /* تحسينات إضافية للأجهزة الصغيرة جداً */
            .explorer-item-actions {
                left: 3px !important;
                padding: 1px 2px !important;
                border-radius: 2px !important;
                box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15) !important;
            }

            .explorer-item-action {
                width: 16px !important;
                height: 16px !important;
                font-size: 9px !important;
                border-radius: 2px !important;
                margin: 0 1px !important;
            }

            .explorer-item-content {
                padding-left: 45px !important; /* مساحة أكبر للأزرار */
                font-size: 14px;
            }

            /* تحسين الاستجابة للمس */
            .explorer-item-action:active {
                transform: scale(0.8);
                background: var(--primary-color) !important;
                color: #ffffff !important;
            }

            /* تحسينات أزرار المحادثات للأجهزة الصغيرة جداً */
            .conversation-actions {
                left: 3px !important;
                padding: 1px 2px !important;
                border-radius: 2px !important;
                box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15) !important;
            }

            .conversation-action {
                width: 16px !important;
                height: 16px !important;
                font-size: 9px !important;
                border-radius: 2px !important;
                margin: 0 1px !important;
            }

            .conv-title {
                padding-left: 40px !important;
                font-size: 12px;
            }

            .conversation-action:active {
                transform: scale(0.8);
                background: var(--primary-color) !important;
                color: #ffffff !important;
            }
        }

        .open-code-btn {
            background: #5e35b1;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 6px;
            display: inline-flex;
            align-items: center;
        }

        .open-code-btn:hover {
            background: #4527a0;
            transform: translateY(-1px);
        }

        .open-code-btn i {
            margin-right: 4px;
        }

        /* إعادة تنظيم أزرار شريط الكود */
        .code-header {
            justify-content: flex-start !important;
        }

        .language-label {
            margin-right: auto;
        }

        /* تعديل أنماط الأزرار الحالية */
        .copy-button {
            order: 2;
        }

        .open-code-btn {
            order: 3;
        }

        .run-code-btn {
            order: 4;
        }

        /* نافذة عرض المواقع الجانبية */
        .web-preview-sidebar {
            position: fixed;
            top: 20px;
            left: 20px; /* تغيير من right إلى left للتوافق مع التعديلات في الجافاسكريبت */
            width: 480px;
            height: calc(100% - 40px);
            background: var(--bg-dark);
            box-shadow: 0 0 20px rgba(0,0,0,0.4);
            z-index: 3000;
            display: flex;
            flex-direction: column;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: opacity 0.3s cubic-bezier(0.4,0,0.2,1), transform 0.3s cubic-bezier(0.4,0,0.2,1);
            resize: both;
            overflow: hidden;
            min-width: 320px;
            min-height: 300px;
        }

        .web-preview-sidebar.minimized {
            height: 40px !important;
            overflow: hidden;
        }

        .web-preview-sidebar.maximized {
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            border-radius: 0;
        }

        .web-preview-sidebar[style*="display:none"] {
            transform: translateX(100%);
            opacity: 0;
        }

        .web-preview-header {
            height: 40px;
            min-height: 40px;
            background: var(--bg-medium);
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px 8px 0 0;
            cursor: move;
            flex-wrap: nowrap;
            overflow: hidden;
        }

        .preview-drag-handle {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: move;
            min-width: 0;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .preview-drag-handle span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .preview-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap;
            flex-shrink: 0;
        }

        .preview-btn {
            background: transparent;
            border: none;
            color: var(--text-light);
            width: 28px;
            height: 28px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .preview-btn:hover {
            background: rgba(255,255,255,0.1);
        }

        .preview-btn.preview-close:hover {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .browser-controls {
            height: 36px;
            background: var(--bg-light);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .browser-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;
        }

        .browser-btn {
            background: transparent;
            border: none;
            color: var(--text-light);
            width: 28px;
            height: 28px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .browser-btn:hover {
            background: rgba(255,255,255,0.1);
        }

        .browser-address-bar {
            flex: 1;
            height: 28px;
            background: var(--bg-dark);
            border-radius: 14px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: var(--text-light);
            font-size: 12px;
            gap: 6px;
        }

        .browser-address-bar i {
            color: #4caf50;
            font-size: 10px;
        }

        .device-select-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .device-select-wrapper i {
            position: absolute;
            right: 8px;
            pointer-events: none;
            font-size: 12px;
            opacity: 0.7;
        }

        #device-selector {
            background: var(--bg-dark);
            color: var(--text-light);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 4px 24px 4px 8px;
            font-size: 12px;
            appearance: none;
            cursor: pointer;
        }

        /* اجعل iframe يأخذ كل مساحة preview-container دائماً */
        .preview-container {
            position: relative;
            width: 100%;
            height: 100%;
            min-height: 0;
            min-width: 0;
            padding: 0 !important;
            display: flex;
            flex-direction: column;
            justify-content: stretch;
            align-items: stretch;
            box-sizing: border-box;
        }

        #web-preview-iframe {
            width: 100% !important;
            height: 100% !important;
            min-width: 0;
            min-height: 0;
            display: block;
            position: relative;
            border: none;
            background: white;
            box-sizing: border-box;
        }

        /* إخفاء خيارات device-selector على الشاشات الصغيرة نهائياً */
        @media (max-width: 700px) {
            .device-select-wrapper {
                display: none !important;
            }
        }

        .device-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            position: relative;
            margin: 0 auto;
            transform-style: preserve-3d;
            perspective: 1000px;
            box-sizing: border-box;
            padding: 40px 20px;
            overflow: visible;
            min-height: 200px;
        }

        /* تحسين أساسي لجميع الأجهزة */
        .device-frame {
            position: relative;
            background: #fff;
            overflow: hidden;
            transition: all 0.3s ease-in-out;
            margin: 20px auto !important;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            transform-origin: center center !important;
            left: 0 !important;
            right: 0 !important;
            box-sizing: content-box; /* لضمان احتساب الحدود خارج حجم المحتوى */
            max-width: 100%;
            max-height: 100%;
        }

        /* تحسين عرض الإطار في الوضع المتجاوب */
        .device-frame.responsive {
            width: 100% !important;
            height: 100% !important;
            border: none !important;
            box-shadow: none !important;
            transform: none !important;
            margin: 0 !important;
            max-width: 100%;
            max-height: 100%;
            overflow: auto;
        }

        /* تحسين عرض الإطار في وضع الهاتف */
        .device-frame.mobile {
            width: 375px;
            height: 667px;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            border: 10px solid #333;
            max-width: calc(100% - 20px);
            max-height: calc(100% - 20px);
        }

        /* تحسين عرض الإطار في وضع التابلت */
        .device-frame.tablet {
            width: 768px;
            height: 1024px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            border: 12px solid #333;
            max-width: calc(100% - 24px);
            max-height: calc(100% - 24px);
        }

        /* تحسين عرض الإطار في وضع اللابتوب */
        .device-frame.laptop {
            width: 1366px;
            height: 768px;
            border-radius: 6px 6px 0 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            border: 12px solid #333;
            border-bottom: 40px solid #333;
            max-width: calc(100% - 24px);
            max-height: calc(100% - 52px);
        }

        /* تحسين عرض الإطار في وضع سطح المكتب */
        .device-frame.desktop {
            width: 1920px;
            height: 1080px;
            border-radius: 6px 6px 0 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            border: 10px solid #1e1e1e;
            border-bottom: 25px solid #1e1e1e;
            max-width: calc(100% - 20px);
            max-height: calc(100% - 35px);
        }

        /* تحسين مقياس الإطار للأجهزة المختلفة */
        @media (max-width: 576px) {
            .preview-container {
                padding: 5px;
        }

            .device-frame {
                transform-origin: center center !important;
            }

            .device-frame.mobile {
                transform: scale(0.9) !important;
                max-width: 90%;
                max-height: 90%;
        }

            .device-frame.tablet,
            .device-frame.laptop,
            .device-frame.desktop {
                transform: scale(0.5) !important;
                transform-origin: center center !important;
            }
        }

        /* تحسين مقياس الإطار للأجهزة المتوسطة */
        @media (min-width: 577px) and (max-width: 992px) {
            .device-frame.mobile {
                transform: scale(1) !important;
        }

            .device-frame.tablet {
                transform: scale(0.7) !important;
            }

            .device-frame.laptop,
            .device-frame.desktop {
                transform: scale(0.5) !important;
        }
        }

        /* تحسين مقياس الإطار للأجهزة الكبيرة */
        @media (min-width: 993px) and (max-width: 1400px) {
            .device-frame.mobile {
                transform: scale(1) !important;
        }

            .device-frame.tablet {
                transform: scale(0.8) !important;
        }

            .device-frame.laptop {
                transform: scale(0.6) !important;
            }

            .device-frame.desktop {
                transform: scale(0.5) !important;
        }
        }

        /* تحسين العرض في وضع الشاشة الأفقية للأجهزة الصغيرة */
        @media (orientation: landscape) and (max-height: 600px) {
            .device-frame.mobile {
                transform: rotate(90deg) scale(0.6) !important;
                margin: 40px auto !important;
            }

            .preview-container {
                padding: 10px;
        }
        }

        /* تحسين مظهر الصفحة داخل iframe */
        #web-preview-iframe {
            background-color: white;
            transition: opacity 0.3s ease;
        }

        #web-preview-iframe.loading {
            opacity: 0.5;
        }

        /* مؤشر التحميل */
        .preview-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .preview-loading.active {
            display: block;
        }

        .preview-loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(150, 150, 150, 0.2);
            border-radius: 50%;
            border-top-color: var(--highlight-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* تحسين أزرار التحكم في نافذة المعاينة */
        .web-preview-sidebar .preview-btn {
            outline: none !important;
        }

        .web-preview-header {
            touch-action: none; /* لمنع السحب غير المقصود على الأجهزة اللمسية */
        }

        /* تصحيح مشاكل التجاوب عند تدوير الشاشة */
        @media (orientation: landscape) and (max-height: 500px) {
            .device-frame.mobile {
                transform: rotate(90deg) scale(0.6) !important;
                transform-origin: center center;
                margin: 60px auto;
                left: 0 !important;
        }

            .preview-container {
            align-items: center;
        }
        }

        /* تحسين تجاوب جهاز التابلت */
        @media (max-width: 800px) {
            .device-frame.tablet {
                transform: scale(0.7) !important;
                margin: 0 auto;
            }
        }

        /* تحسين تجاوب أجهزة سطح المكتب واللابتوب */
        @media (max-width: 1200px) {
            .device-frame.desktop, .device-frame.laptop {
                transform: scale(0.5) !important;
                margin: 0 auto;
        }
        }

        /* ضمان صحة نسبة العرض إلى الارتفاع عند تغيير حجم النافذة */
        .device-frame[style*="transform"] {
            margin: 0 auto;
            position: relative;
            left: 0 !important;
            transform-origin: center top !important;
            }

        .web-preview-sidebar.maximized .preview-container {
            height: calc(100% - 80px);
        }

        .web-preview-sidebar.maximized .device-frame {
            transform: scale(1) !important;
        }

        .preview-resize-handle {
            position: absolute;
            bottom: 0;
                right: 0;
            width: 20px;
            height: 20px;
            cursor: nwse-resize;
            background: transparent;
        }

        .preview-resize-handle::before {
            content: "";
                position: absolute;
            right: 4px;
            bottom: 4px;
            width: 12px;
            height: 12px;
            border-right: 2px solid var(--border-color);
            border-bottom: 2px solid var(--border-color);
            opacity: 0.7;
        }

        /* تأثير التحديث الدوار */
        @keyframes rotating {
            from {
                transform: rotate(0deg);
        }
            to {
                transform: rotate(360deg);
        }
        }

        .preview-btn.rotating i {
            animation: rotating 0.5s linear;
        }

        /* نمط اختيار الجهاز */
        .device-select-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            margin: 0 5px;
        }

        .device-select-wrapper i {
            margin-left: 5px;
            font-size: 14px;
            color: var(--text-dim);
            transition: color 0.3s ease;
        }

        #device-selector {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: 2px 5px;
            font-size: 12px;
            border-radius: 4px;
            cursor: pointer;
            outline: none;
            transition: border-color 0.3s ease;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            padding-right: 20px;
        }

        #device-selector:hover, #device-selector:focus {
            border-color: var(--highlight-color);
        }

        .device-select-wrapper::after {
            content: '\f0d7';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-dim);
            pointer-events: none;
        }

        /* تجاوب أفضل للأجهزة الصغيرة */
        @media (max-width: 700px) {
            .web-preview-sidebar {
            width: 100% !important;
            height: 100% !important;
                top: 0 !important;
                right: 0 !important;
                border-radius: 0;
                resize: none;
            }

            .preview-container {
                padding: 5px;
        }

            .device-frame {
                transform: scale(0.9) !important;
        }

            .browser-controls {
                padding: 2px 5px;
        }

            .web-preview-header {
                padding: 5px 10px;
        }

            .preview-controls {
                flex-wrap: wrap;
            }
        }

        /* أنماط شريط أدوات المفتش */
        .inspector-toolbar {
            display: flex;
            background: var(--bg-darker);
            border-bottom: 1px solid var(--border-color);
            padding: 4px 8px;
            align-items: center;
            font-size: 12px;
            justify-content: space-between;
            height: 32px;
            color: var(--text-dim);
        }

        .inspector-tools {
            display: flex;
            align-items: center;
        }

        .inspector-btn {
            background: transparent;
            border: none;
            color: var(--text-dim);
            width: 24px;
            height: 24px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 2px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .inspector-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }

        .inspector-btn[data-active="true"] {
            background: var(--highlight-color);
            color: #fff;
        }

        .inspector-separator {
            width: 1px;
            height: 16px;
            background: var(--border-color);
            margin: 0 6px;
        }

        .device-size {
            font-family: 'Consolas', monospace;
            font-size: 11px;
            padding: 0 8px;
            color: var(--text-color);
        }

        .inspector-device-controls {
            display: flex;
            align-items: center;
        }

        #device-zoom, #network-throttle {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: 1px 4px;
            font-size: 11px;
            border-radius: 3px;
            margin: 0 4px;
            outline: none;
        }

        .network-select {
            position: relative;
            display: flex;
            align-items: center;
        }

        .network-select i {
            margin-left: 4px;
            font-size: 12px;
            color: var(--text-dim);
        }

        /* شريط المعلومات في الأسفل */
        .inspector-status-bar {
            display: flex;
            background: var(--bg-darker);
            border-top: 1px solid var(--border-color);
            padding: 4px 8px;
            align-items: center;
            font-size: 11px;
            justify-content: space-between;
            height: 22px;
            color: var(--text-dim);
        }

        .element-path {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-family: 'Consolas', monospace;
            max-width: 70%;
        }

        .style-info {
            font-family: 'Consolas', monospace;
            display: flex;
            align-items: center;
        }

        .status-separator {
            margin: 0 5px;
            color: var(--border-color);
        }

        /* طبقات المفتش */
        .inspector-overlay {
            position: absolute;
            top: 0;
            left: 0;
                width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .element-highlighter {
            position: absolute;
            border: 2px solid #6464ff;
            background-color: rgba(100, 100, 255, 0.1);
            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2);
            pointer-events: none;
            z-index: 101;
            display: none;
            }

        .element-info {
            position: absolute;
            background-color: #333;
            color: #fff;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-family: 'Consolas', monospace;
            pointer-events: none;
            z-index: 102;
            white-space: nowrap;
            display: none;
        }

        .measurement-overlay {
            position: absolute;
            pointer-events: none;
            z-index: 103;
            display: none;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
                width: 100%;
            height: 100%;
            background-image: linear-gradient(to right, rgba(120, 120, 120, 0.1) 1px, transparent 1px),
                              linear-gradient(to bottom, rgba(120, 120, 120, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
            z-index: 99;
        }

        /* تعديلات على مكونات المفتش */
        .web-preview-sidebar {
            display: flex;
            flex-direction: column;
            height: calc(100% - 40px);
        }

        .preview-container {
            position: relative;
            flex: 1;
            overflow: auto;
        }

        /* تحسينات المتصفح */
        .browser-controls {
            padding: 3px 4px;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-dark);
            height: 28px;
        }

        .browser-actions {
            height: 22px;
        }

        .browser-address-bar {
            border-radius: 3px;
            font-size: 11px;
        }

        /* زر تحويل وضع المفتش */
        .inspector-mode-toggle {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: var(--highlight-color);
            color: #fff;
            border: none;
            border-radius: 50%;
                width: 36px;
                height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 200;
            transition: all 0.2s ease;
        }

        .inspector-mode-toggle:hover {
            transform: scale(1.05);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
        }

        /* تنسيق عام للمحاكي */
        .web-preview-sidebar.inspector-mode .inspector-toolbar,
        .web-preview-sidebar.inspector-mode .inspector-status-bar {
            display: flex;
        }

        .web-preview-sidebar:not(.inspector-mode) .inspector-toolbar,
        .web-preview-sidebar:not(.inspector-mode) .inspector-status-bar {
            display: none;
        }

        /* تعديلات على الأجهزة في وضع المفتش */
        .web-preview-sidebar.inspector-mode .device-frame {
            border-color: #222 !important;
            }

        /* تصميم التيرمنال المقسم */
        .executor-footer.split {
            display: flex;
            flex-direction: column;
            }

        .executor-footer.split .terminal {
            flex: 1;
            height: 50%;
            overflow-y: auto;
            }

        .executor-footer.split #executor-result {
            border-bottom: 1px solid var(--border-color);
            }

        .executor-footer.split .second-terminal {
            border-top: 1px solid var(--border-color);
            }

        /* قائمة خيارات التيرمنال */
        .terminal-options-menu {
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            min-width: 180px;
            z-index: 1000;
            overflow: hidden;
        }

        .terminal-option {
            padding: 8px 12px;
            color: var(--text-light);
            cursor: pointer;
            font-size: 13px;
            transition: background-color 0.2s;
            }

        .terminal-option:hover {
            background-color: var(--bg-light);
            }

        /* قائمة خيارات المحرر */
        .editor-options-menu {
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            min-width: 180px;
            z-index: 1000;
            overflow: hidden;
            }

        .editor-option {
            padding: 8px 12px;
            color: var(--text-light);
            cursor: pointer;
            font-size: 13px;
            transition: background-color 0.2s;
            }

        .editor-option:hover {
            background-color: var(--bg-light);
            }

        /* تحسين أزرار المحرر السريعة */
        .quick-actions {
            position: absolute;
            top: 8px;
                right: 8px;
            display: flex;
            gap: 4px;
            z-index: 100;
            background-color: rgba(30, 30, 30, 0.6);
            border-radius: 4px;
            padding: 2px;
        }

        .quick-action {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-medium);
            cursor: pointer;
            border-radius: 3px;
            transition: all 0.2s;
        }

        .quick-action:hover {
            background-color: var(--bg-light);
            color: var(--text-light);
        }

        /* تحسين مساحة المحرر */
        .monaco-editor .monaco-scrollable-element {
            padding-bottom: 20px !important;
        }

        .monaco-editor .margin {
            padding-bottom: 20px !important;
        }

        /* تحسين سمات التيرمنال */
        .terminal.theme-dark {
            background: var(--bg-darker);
            color: #e0e0e0;
        }

        .terminal.theme-light {
            background: #f5f5f5;
            color: #333;
        }

        .terminal.theme-blue {
            background: #1a2233;
            color: #a9c3e2;
        }

        .terminal.theme-green {
            background: #1a2a1a;
            color: #a8e2a9;
        }

        .terminal.theme-amber {
            background: #2a2010;
            color: #e2c088;
        }

        /* تحسين تجاوب نافذة المعاينة لجميع الأجهزة */
        @media (max-width: 1200px) {
            .web-preview-sidebar {
                width: 80% !important;
                max-width: 800px;
        }

            .device-frame.desktop, .device-frame.laptop {
                transform: scale(0.6) !important;
                margin: 20px auto !important;
            }
        }

        @media (max-width: 992px) {
            .web-preview-sidebar {
                width: 90% !important;
                left: 5% !important;
                right: 5% !important;
        }

            .device-frame.tablet {
                transform: scale(0.7) !important;
                margin: 20px auto !important;
            }
        }

        @media (max-width: 768px) {
            .web-preview-sidebar {
                width: 95% !important;
                left: 2.5% !important;
                right: 2.5% !important;
                height: 90% !important;
                top: 5% !important;
        }

            .device-frame {
                transform: scale(0.8) !important;
        }

            .preview-controls {
                flex-wrap: wrap;
                gap: 4px;
        }
        }

        /* تحسين تجاوب نافذة المعاينة للأجهزة الصغيرة جدًا */
        @media (max-width: 576px) {
            .web-preview-sidebar {
                width: 100% !important;
                height: 100% !important;
                top: 0 !important;
                left: 0 !important;
                border-radius: 0;
                resize: none;
        }

            .web-preview-header {
                border-radius: 0;
        }

            .device-frame.mobile {
                transform: scale(0.9) !important;
                margin: 10px auto !important;
        }

            .device-frame.tablet,
            .device-frame.laptop,
            .device-frame.desktop {
                transform: scale(0.5) !important;
                margin: 10px auto !important;
            }

            .preview-controls {
                gap: 2px;
            }

            .preview-controls .device-select-wrapper {
                max-width: 120px;
            }

            #device-selector {
                width: 100%;
                font-size: 12px;
                padding-right: 20px;
            }
        }

        /* تصحيح عرض نافذة المعاينة في وضع التكبير على جميع الأجهزة */
        .web-preview-sidebar.maximized {
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            border-radius: 0;
            resize: none;
        }

        /* تحسين عرض الإطار في الأجهزة المحمولة في الوضع الأفقي */
        @media (orientation: landscape) and (max-height: 600px) {
            .web-preview-sidebar {
                height: 100% !important;
                top: 0 !important;
        }

            .preview-container {
                padding: 5px;
        }

            .device-frame.mobile {
                transform: rotate(90deg) scale(0.6) !important;
                margin: 40px auto !important;
            }
        }

        /* تحسين عرض أزرار التحكم في الأجهزة الصغيرة */
        @media (max-width: 480px) {
            .preview-controls .preview-btn {
                width: 28px;
                height: 28px;
        }

            .preview-drag-handle span {
                font-size: 12px;
        }

            .device-select-wrapper {
                max-width: 100px;
            }
        }

        .preview-drag-handle i {
            font-size: 12px;
            opacity: 0.7;
        }

        /* تحسين تجاوب رأس نافذة المعاينة للأجهزة الصغيرة */
        @media (max-width: 576px) {
            .web-preview-header {
                padding: 0 5px;
                height: 36px;
                min-height: 36px;
                border-radius: 0;
            }

            .preview-drag-handle {
            gap: 4px;
            }

            .preview-drag-handle span {
            font-size: 12px;
                max-width: 80px;
            }

            .preview-controls {
                gap: 2px;
            }

            .preview-controls .preview-btn {
                width: 24px;
                height: 24px;
            }

            .preview-controls .device-select-wrapper {
                max-width: 100px;
            }

            #device-selector {
                width: 100%;
                font-size: 11px;
                padding: 2px 18px 2px 4px;
            }
        }

        /* تحسين تجاوب رأس نافذة المعاينة للأجهزة المتوسطة */
        @media (min-width: 577px) and (max-width: 768px) {
            .web-preview-header {
                padding: 0 8px;
            }

            .preview-drag-handle span {
                max-width: 120px;
            }

            .preview-controls {
                gap: 4px;
            }

            .preview-controls .device-select-wrapper {
                max-width: 140px;
            }
        }

        /* تحسين عرض أزرار التحكم في الأجهزة الصغيرة */
        @media (max-width: 480px) {
            .preview-controls .preview-btn {
                width: 22px;
                height: 22px;
                font-size: 12px;
            }

            .preview-drag-handle i {
                font-size: 10px;
            }

            .preview-drag-handle span {
                font-size: 11px;
                max-width: 60px;
            }

            .device-select-wrapper {
                max-width: 80px;
            }

            #device-selector {
                padding-right: 16px;
            }

            .device-select-wrapper i {
                right: 4px;
            }
        }

        /* تحسين عرض أزرار التحكم في الوضع الأفقي للأجهزة الصغيرة */
        @media (orientation: landscape) and (max-height: 500px) {
            .web-preview-header {
                height: 32px;
                min-height: 32px;
            }

            .preview-controls .preview-btn {
                width: 24px;
                height: 24px;
            }
        }

        /* أنماط مخططات Mermaid */
        .mermaid-container {
            position: relative;
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            margin: 12px 0;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .mermaid-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: var(--bg-darker);
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
            color: var(--text-medium);
        }

        .mermaid-title {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .mermaid-title i {
            color: var(--primary-color);
        }

        .mermaid-controls {
            display: flex;
            align-items: center;
            gap: 3px;
            margin-bottom: 6px;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .mermaid-btn {
            padding: 3px 6px;
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: 3px;
            color: var(--text-light);
            cursor: pointer;
            font-size: 9px;
            font-weight: 500;
            transition: all var(--transition-fast);
            display: inline-flex;
            align-items: center;
            gap: 2px;
            min-width: 24px;
            height: 24px;
            justify-content: center;
            white-space: nowrap;
            line-height: 1;
        }

        .mermaid-btn:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .mermaid-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        /* أنماط قائمة التحميل */
        .mermaid-download-menu {
            position: relative;
            display: inline-block;
        }

        .mermaid-download-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-darker);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-shadow: var(--shadow-md);
            z-index: 1000;
            min-width: 140px;
            margin-top: 2px;
        }

        .mermaid-download-dropdown.show {
            display: block;
        }

        .mermaid-download-option {
            padding: 8px 12px;
            cursor: pointer;
            color: var(--text-light);
            font-size: 11px;
            border-bottom: 1px solid var(--border-color);
            transition: background var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .mermaid-download-option:last-child {
            border-bottom: none;
        }

        .mermaid-download-option:hover {
            background: var(--primary-color);
            color: white;
        }

        .mermaid-download-option i {
            width: 12px;
            text-align: center;
        }

        /* أنماط أزرار محسنة ومنظمة */

        /* أزرار التحكم الأساسية */
        .mermaid-btn.view-btn,
        .mermaid-btn.code-btn,
        .mermaid-btn.copy-btn {
            min-width: 40px;
            padding: 3px 8px;
            font-size: 9px;
        }

        /* أزرار التكبير والتصغير */
        .mermaid-btn.zoom-in-btn,
        .mermaid-btn.zoom-out-btn,
        .mermaid-btn.reset-zoom-btn {
            min-width: 24px;
            padding: 3px 4px;
            font-size: 8px;
        }

        .mermaid-btn.zoom-in-btn:hover {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }

        .mermaid-btn.zoom-out-btn:hover {
            background: #f59e0b;
            border-color: #f59e0b;
            color: white;
        }

        .mermaid-btn.reset-zoom-btn:hover {
            background: #6b7280;
            border-color: #6b7280;
            color: white;
        }

        /* زر التحميل */
        .mermaid-btn.download-btn {
            min-width: 40px;
            padding: 3px 8px;
            font-size: 9px;
        }

        /* زر التصميم */
        .mermaid-btn.theme-btn {
            min-width: 24px;
            padding: 3px 4px;
            font-size: 8px;
        }

        .mermaid-btn.theme-btn:hover {
            background: #8b5cf6;
            border-color: #8b5cf6;
            color: white;
        }

        /* أنماط قائمة التصميم */
        .mermaid-theme-menu {
            position: relative;
            display: inline-block;
        }

        .mermaid-theme-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-darker);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-shadow: var(--shadow-md);
            z-index: 1000;
            min-width: 120px;
            margin-top: 2px;
        }

        .mermaid-theme-dropdown.show {
            display: block;
        }

        .mermaid-theme-option {
            padding: 8px 12px;
            cursor: pointer;
            color: var(--text-light);
            font-size: 11px;
            border-bottom: 1px solid var(--border-color);
            transition: background var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .mermaid-theme-option:last-child {
            border-bottom: none;
        }

        .mermaid-theme-option:hover {
            background: var(--primary-color);
            color: white;
        }

        .mermaid-theme-option i {
            width: 12px;
            text-align: center;
        }

        /* أنماط المخطط القابل للتكبير */
        .mermaid-diagram {
            overflow: hidden;
            position: relative;
            cursor: grab;
            transition: transform 0.3s ease;
        }

        .mermaid-diagram.zoomed {
            cursor: move;
        }

        .mermaid-diagram svg {
            max-width: none;
            transition: transform 0.3s ease;
        }

        /* مؤشر التكبير محسن */
        .zoom-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 600;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.3s ease;
            pointer-events: none;
            transform: translateY(-5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .zoom-indicator.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* حالات التكبير والسحب */
        .mermaid-diagram.zoomed {
            cursor: grab;
            overflow: hidden;
        }

        .mermaid-diagram.dragging {
            cursor: grabbing;
            user-select: none;
        }

        .mermaid-diagram.zooming {
            touch-action: none;
            user-select: none;
        }

        /* تحسينات للتكبير باللمس مع دقة عالية */
        .mermaid-diagram svg {
            transition: transform 0.2s ease-out;
            transform-origin: center center;
            touch-action: manipulation;
            will-change: transform;

            /* تحسين الدقة ومنع التبكسل */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            shape-rendering: geometricPrecision;
            text-rendering: geometricPrecision;

            /* تحسين الحواف */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;

            /* منع التشويش */
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;

            /* تحسين الأداء */
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        .mermaid-diagram.zooming svg {
            transition: none;
        }

        /* تحسين دقة النصوص والعناصر داخل SVG */
        .mermaid-diagram svg text {
            /* تحسين وضوح النص */
            text-rendering: geometricPrecision;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-smooth: always;

            /* منع التشويش */
            shape-rendering: crispEdges;
            vector-effect: non-scaling-stroke;
        }

        .mermaid-diagram svg path,
        .mermaid-diagram svg line,
        .mermaid-diagram svg polyline,
        .mermaid-diagram svg polygon {
            /* تحسين دقة الخطوط والأشكال */
            shape-rendering: geometricPrecision;
            vector-effect: non-scaling-stroke;

            /* منع التشويش في الحواف */
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        .mermaid-diagram svg rect,
        .mermaid-diagram svg circle,
        .mermaid-diagram svg ellipse {
            /* تحسين دقة الأشكال الهندسية */
            shape-rendering: geometricPrecision;
            vector-effect: non-scaling-stroke;
        }

        /* تحسين دقة الخطوط المنقطة */
        .mermaid-diagram svg [stroke-dasharray] {
            shape-rendering: geometricPrecision;
            vector-effect: non-scaling-stroke;
        }

        /* تحسين دقة الأسهم */
        .mermaid-diagram svg marker {
            shape-rendering: geometricPrecision;
        }

        /* تحسينات إضافية للدقة العالية */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .mermaid-diagram svg {
                /* تحسين للشاشات عالية الدقة */
                image-rendering: -webkit-optimize-contrast;
                image-rendering: optimize-contrast;
                -ms-interpolation-mode: nearest-neighbor;
            }
        }

        /* تحسين الدقة للتكبير العالي */
        .mermaid-diagram.high-zoom svg {
            image-rendering: crisp-edges;
            shape-rendering: crispEdges;
        }

        .mermaid-diagram.high-zoom svg text {
            text-rendering: optimizeLegibility;
            font-smooth: never;
            -webkit-font-smoothing: none;
        }

        /* منع التشويش في المتصفحات المختلفة */
        .mermaid-diagram svg {
            /* Firefox */
            -moz-crisp-edges: true;

            /* Safari */
            -webkit-optimize-contrast: true;

            /* Chrome/Edge */
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        /* تحسين الاستجابة للمس */
        .mermaid-diagram {
            touch-action: pan-x pan-y;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .mermaid-diagram.zooming {
            touch-action: none;
        }

        /* تحذير التكبير المفرط */
        .zoom-warning {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(220, 38, 38, 0.9);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            z-index: 100;
            animation: pulse 1s infinite;
            pointer-events: none;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* مؤشرات بصرية للتكبير */
        .mermaid-diagram.zoomed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            pointer-events: none;
            z-index: 1;
        }

        /* تحسين أزرار التكبير */
        .mermaid-btn.zoom-in-btn:hover {
            background: #10b981;
            transform: scale(1.1);
        }

        .mermaid-btn.zoom-out-btn:hover {
            background: #f59e0b;
            transform: scale(1.1);
        }

        .mermaid-btn.zoom-reset-btn:hover {
            background: #6b7280;
            transform: scale(1.1);
        }

        /* تنظيم الأزرار في مجموعات */
        .mermaid-controls {
            display: flex;
            align-items: center;
            gap: 2px;
            margin-bottom: 6px;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        /* مجموعة أزرار التحكم الأساسية */
        .mermaid-controls .control-group {
            display: flex;
            gap: 2px;
            margin-left: 4px;
            padding: 1px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.05);
        }

        .mermaid-controls .control-group:first-child {
            margin-left: 0;
        }

        /* تصميم متجاوب محسن */
        @media (max-width: 1024px) {
            .mermaid-btn.view-btn,
            .mermaid-btn.code-btn,
            .mermaid-btn.copy-btn {
                min-width: 36px;
                padding: 3px 6px;
                font-size: 8px;
            }

            .mermaid-btn.download-btn {
                min-width: 36px;
                padding: 3px 6px;
                font-size: 8px;
            }
        }

        @media (max-width: 768px) {
            .mermaid-controls {
                gap: 2px;
                justify-content: center;
            }

            .mermaid-controls .control-group {
                margin-left: 2px;
            }

            .mermaid-btn {
                min-width: 22px;
                height: 22px;
                padding: 2px 4px;
                font-size: 8px;
            }

            .mermaid-btn.view-btn,
            .mermaid-btn.code-btn,
            .mermaid-btn.copy-btn {
                min-width: 32px;
                padding: 2px 6px;
            }

            .mermaid-btn.download-btn {
                min-width: 32px;
                padding: 2px 6px;
            }

            .mermaid-btn i {
                font-size: 8px;
            }

            .mermaid-download-dropdown,
            .mermaid-theme-dropdown {
                min-width: 90px;
                font-size: 9px;
            }

            .mermaid-download-option,
            .mermaid-theme-option {
                padding: 4px 6px;
                font-size: 9px;
            }

            .zoom-indicator {
                font-size: 9px;
                padding: 2px 4px;
            }
        }

        @media (max-width: 480px) {
            .mermaid-controls {
                justify-content: center;
                gap: 1px;
            }

            .mermaid-controls .control-group {
                margin-left: 1px;
                gap: 1px;
            }

            .mermaid-btn {
                min-width: 20px;
                height: 20px;
                padding: 1px 3px;
                font-size: 7px;
                border-radius: 2px;
            }

            .mermaid-btn.view-btn,
            .mermaid-btn.code-btn,
            .mermaid-btn.copy-btn {
                min-width: 28px;
                padding: 1px 4px;
            }

            .mermaid-btn.download-btn {
                min-width: 28px;
                padding: 1px 4px;
            }

            .mermaid-title {
                font-size: 11px;
            }

            .mermaid-diagram {
                min-height: 120px;
            }

            .mermaid-download-dropdown,
            .mermaid-theme-dropdown {
                min-width: 80px;
                font-size: 8px;
            }

            .mermaid-download-option,
            .mermaid-theme-option {
                padding: 3px 4px;
                font-size: 8px;
            }
        }

        /* أنماط التصميمات المختلفة */
        .mermaid-container[data-theme="dark"] .mermaid-diagram {
            background: #1f2937;
        }

        .mermaid-container[data-theme="forest"] .mermaid-diagram {
            background: #f0f9f0;
        }

        .mermaid-container[data-theme="neutral"] .mermaid-diagram {
            background: #f5f5f5;
        }

        .mermaid-container[data-theme="base"] .mermaid-diagram {
            background: #fff5f5;
        }

        /* تأثيرات التفاعل المحسنة */
        .mermaid-btn {
            position: relative;
            overflow: hidden;
        }

        .mermaid-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .mermaid-btn:active::before {
            width: 100px;
            height: 100px;
        }

        /* تحسينات إضافية للتجاوب */
        .mermaid-container {
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .mermaid-container {
                margin: 8px 0;
            }
        }

        /* أنماط السحب والإفلات للتكبير */
        .mermaid-diagram.dragging {
            cursor: grabbing !important;
        }

        .mermaid-diagram.dragging svg {
            pointer-events: none;
        }

        /* أنماط أزرار التكبير والتصغير */
        .zoom-in-btn, .zoom-out-btn, .zoom-reset-btn {
            background: var(--bg-darker) !important;
            border: 1px solid var(--border-color) !important;
        }

        .zoom-in-btn:hover {
            background: #28a745 !important;
            border-color: #28a745 !important;
        }

        .zoom-out-btn:hover {
            background: #dc3545 !important;
            border-color: #dc3545 !important;
        }

        .zoom-reset-btn:hover {
            background: #6c757d !important;
            border-color: #6c757d !important;
        }

        /* أنماط قائمة التصميم */
        .mermaid-theme-menu {
            position: relative;
            display: inline-block;
        }

        .mermaid-theme-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-darker);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-shadow: var(--shadow-md);
            z-index: 1000;
            min-width: 120px;
            margin-top: 2px;
        }

        .mermaid-theme-dropdown.show {
            display: block;
        }

        .mermaid-theme-option {
            padding: 8px 12px;
            cursor: pointer;
            color: var(--text-light);
            font-size: 11px;
            border-bottom: 1px solid var(--border-color);
            transition: background var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .mermaid-theme-option:last-child {
            border-bottom: none;
        }

        .mermaid-theme-option:hover {
            background: var(--primary-color);
            color: white;
        }

        .mermaid-theme-option i {
            width: 12px;
            text-align: center;
        }

        /* أنماط التكبير والتصغير للمخططات */
        .mermaid-diagram {
            transition: transform 0.3s ease;
            transform-origin: center center;
            overflow: hidden;
        }

        .mermaid-diagram.zoomed {
            cursor: grab;
        }

        .mermaid-diagram.zoomed:active {
            cursor: grabbing;
        }

        /* أنماط التصميمات المختلفة */
        .mermaid-container[data-theme="dark"] .mermaid-diagram svg {
            background: #1e1e1e !important;
        }

        .mermaid-container[data-theme="forest"] .mermaid-diagram svg {
            background: #f0f8f0 !important;
        }

        .mermaid-container[data-theme="neutral"] .mermaid-diagram svg {
            background: #f5f5f5 !important;
        }

        .mermaid-container[data-theme="base"] .mermaid-diagram svg {
            background: #fff5f5 !important;
        }

        .mermaid-content {
            position: relative;
            min-height: 200px;
        }

        .mermaid-diagram {
            padding: 20px;
            text-align: center;
            background: white;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mermaid-code {
            display: none;
            padding: 0;
            margin: 0;
            background: var(--bg-darker);
            border: none;
            border-radius: 0;
        }

        .mermaid-code.active {
            display: block;
        }

        .mermaid-code pre {
            margin: 0;
            padding: 16px;
            background: var(--bg-darker);
            color: var(--text-light);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
        }

        .mermaid-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: var(--text-medium);
            font-size: 14px;
        }

        .mermaid-error {
            padding: 16px;
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: #fca5a5;
            font-size: 13px;
            border-radius: 4px;
            margin: 8px;
        }

        /* تحسينات للمخططات */
        .mermaid svg {
            max-width: 100%;
            height: auto;
        }

        /* أنماط خاصة للمخططات المختلفة */
        .mermaid-container[data-type="graph"] .mermaid-title::before {
            content: "📊";
            margin-left: 4px;
        }

        .mermaid-container[data-type="flowchart"] .mermaid-title::before {
            content: "🔄";
            margin-left: 4px;
        }

        .mermaid-container[data-type="sequence"] .mermaid-title::before {
            content: "📋";
            margin-left: 4px;
        }

        .mermaid-container[data-type="gantt"] .mermaid-title::before {
            content: "📅";
            margin-left: 4px;
        }

        /* تأثيرات التفاعل */
        .mermaid-container:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 8px rgba(59, 130, 246, 0.2);
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .mermaid-header {
                flex-direction: column;
                gap: 8px;
                align-items: stretch;
            }

            .mermaid-controls {
                justify-content: center;
            }

            .mermaid-diagram {
                padding: 12px;
            }
        }