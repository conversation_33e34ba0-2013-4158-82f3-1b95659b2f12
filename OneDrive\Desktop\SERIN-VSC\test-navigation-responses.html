<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار التنقل بين الإجابات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            border-radius: 12px;
            padding: 20px;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #3b82f6;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #333;
            border-radius: 8px;
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #10b981;
        }

        .message {
            background: #404040;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #3b82f6;
        }

        .message.bot {
            border-left-color: #10b981;
        }

        .message-content {
            margin-bottom: 10px;
        }

        /* أنماط أزرار التنقل بين الإجابات */
        .response-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 8px;
            padding: 8px 12px;
            background: #4a4a4a;
            border-radius: 6px;
            border: 1px solid #555;
            font-size: 13px;
        }

        .navigation-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .response-counter {
            color: #ccc;
            font-weight: 500;
            font-size: 12px;
        }

        .navigation-buttons {
            display: flex;
            gap: 4px;
        }

        .nav-btn {
            background: transparent;
            border: 1px solid #666;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            padding: 6px 8px;
            font-size: 12px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 32px;
            height: 28px;
        }

        .nav-btn:hover:not(:disabled) {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            transform: translateY(-1px);
        }

        .nav-btn:active:not(:disabled) {
            transform: scale(0.95);
        }

        .nav-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: #555;
        }

        .nav-btn i {
            font-size: 10px;
        }

        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #555;
        }

        .message-action-btn {
            background: transparent;
            border: 1px solid #666;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            padding: 6px 12px;
            font-size: 12px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .message-action-btn:hover {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s ease;
        }

        .test-button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .description {
            color: #ccc;
            font-size: 14px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار أزرار التنقل بين الإجابات</h1>

        <div class="test-section">
            <div class="test-title">1. رسالة بإجابة واحدة (لا تظهر أزرار التنقل)</div>
            <div class="description">عندما تكون هناك إجابة واحدة فقط، لا تظهر أزرار التنقل</div>
            
            <div class="message bot" id="single-response">
                <div class="message-content">
                    هذه هي الإجابة الوحيدة على السؤال. لا توجد إجابات أخرى متاحة.
                </div>
                <div class="message-actions">
                    <button class="message-action-btn">
                        <i class="fas fa-redo"></i>
                        إعادة توليد
                    </button>
                    <button class="message-action-btn">
                        <i class="fas fa-copy"></i>
                        نسخ
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. رسالة بإجابات متعددة (تظهر أزرار التنقل)</div>
            <div class="description">عندما تكون هناك إجابات متعددة، تظهر أزرار التنقل مع العداد</div>
            
            <div class="message bot" id="multiple-responses">
                <div class="message-content">
                    هذه هي الإجابة الأولى على السؤال. يمكنك التنقل بين الإجابات المختلفة.
                </div>
                <div class="response-navigation">
                    <div class="navigation-info">
                        <span class="response-counter">1 من 3</span>
                    </div>
                    <div class="navigation-buttons">
                        <button class="nav-btn prev-btn" disabled title="الإجابة السابقة">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="nav-btn next-btn" title="الإجابة التالية">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
                <div class="message-actions">
                    <button class="message-action-btn">
                        <i class="fas fa-redo"></i>
                        إعادة توليد
                    </button>
                    <button class="message-action-btn">
                        <i class="fas fa-copy"></i>
                        نسخ
                    </button>
                </div>
            </div>

            <div style="margin-top: 15px;">
                <button class="test-button" onclick="simulateNavigation()">محاكاة التنقل بين الإجابات</button>
                <button class="test-button" onclick="addNewResponse()">إضافة إجابة جديدة</button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. اختبار الاستجابة للأجهزة المحمولة</div>
            <div class="description">تصغير النافذة لرؤية التصميم المتجاوب</div>
            
            <div class="message bot" style="max-width: 300px;">
                <div class="message-content">
                    إجابة قصيرة للاختبار على الأجهزة المحمولة.
                </div>
                <div class="response-navigation" style="flex-direction: column; gap: 8px;">
                    <div class="navigation-info" style="justify-content: center;">
                        <span class="response-counter">2 من 4</span>
                    </div>
                    <div class="navigation-buttons" style="justify-content: center; gap: 8px;">
                        <button class="nav-btn prev-btn" title="الإجابة السابقة">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="nav-btn next-btn" title="الإجابة التالية">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
                <div class="message-actions">
                    <button class="message-action-btn">
                        <i class="fas fa-redo"></i>
                        إعادة توليد
                    </button>
                    <button class="message-action-btn">
                        <i class="fas fa-copy"></i>
                        نسخ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // محاكاة بيانات الإجابات المتعددة
        const responses = [
            "هذه هي الإجابة الأولى على السؤال. يمكنك التنقل بين الإجابات المختلفة.",
            "هذه هي الإجابة الثانية المولدة. تحتوي على معلومات مختلفة قليلاً.",
            "الإجابة الثالثة تقدم منظوراً آخر للموضوع نفسه."
        ];

        let currentIndex = 0;

        function simulateNavigation() {
            const messageElement = document.getElementById('multiple-responses');
            const contentElement = messageElement.querySelector('.message-content');
            const counterElement = messageElement.querySelector('.response-counter');
            const prevBtn = messageElement.querySelector('.prev-btn');
            const nextBtn = messageElement.querySelector('.next-btn');

            // التنقل للإجابة التالية
            currentIndex = (currentIndex + 1) % responses.length;
            
            // تحديث المحتوى
            contentElement.textContent = responses[currentIndex];
            counterElement.textContent = `${currentIndex + 1} من ${responses.length}`;
            
            // تحديث حالة الأزرار
            prevBtn.disabled = currentIndex === 0;
            nextBtn.disabled = currentIndex === responses.length - 1;
            
            // تأثير انتقال
            messageElement.style.opacity = '0.7';
            setTimeout(() => {
                messageElement.style.opacity = '1';
            }, 150);
        }

        function addNewResponse() {
            responses.push(`إجابة جديدة رقم ${responses.length + 1} تم إضافتها للاختبار.`);
            
            const messageElement = document.getElementById('multiple-responses');
            const counterElement = messageElement.querySelector('.response-counter');
            const nextBtn = messageElement.querySelector('.next-btn');
            
            counterElement.textContent = `${currentIndex + 1} من ${responses.length}`;
            nextBtn.disabled = currentIndex === responses.length - 1;
            
            alert(`تم إضافة إجابة جديدة! العدد الإجمالي: ${responses.length}`);
        }
    </script>
</body>
</html>
