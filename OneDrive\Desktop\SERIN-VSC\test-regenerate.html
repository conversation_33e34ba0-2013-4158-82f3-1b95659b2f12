<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار خاصية إعادة التوليد - Serinix</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/2.3.10/purify.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--bg-dark);
            color: var(--text-light);
            font-family: 'Tajawal', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-medium);
            border-radius: var(--radius-lg);
            padding: 20px;
            border: 1px solid var(--border-color);
        }
        
        .test-title {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 30px;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-darker);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .test-section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .test-description {
            margin-bottom: 20px;
            line-height: 1.6;
            color: var(--text-dim);
        }
        
        .demo-message {
            margin: 15px 0;
        }
        
        .demo-message.bot {
            background: var(--bg-medium);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 16px;
            position: relative;
        }
        
        .demo-message.user {
            background: var(--primary-color);
            border-radius: var(--radius-md);
            padding: 12px 16px;
            margin-left: auto;
            margin-right: 20px;
            max-width: 70%;
        }
        
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-family: 'Tajawal', sans-serif;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 5px;
        }
        
        .test-button:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: var(--success-color);
            width: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: var(--radius-sm);
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .status.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">
            <i class="fas fa-redo"></i>
            اختبار خاصية إعادة التوليد
        </h1>
        
        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> نظرة عامة</h3>
            <div class="test-description">
                تم إضافة خاصية إعادة التوليد مثل ChatGPT التي تتيح للمستخدمين:
            </div>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> إعادة توليد آخر رد من البوت</li>
                <li><i class="fas fa-check"></i> نسخ محتوى الرسالة إلى الحافظة</li>
                <li><i class="fas fa-check"></i> تحرير الرسالة (قريباً)</li>
                <li><i class="fas fa-check"></i> تأثيرات بصرية أثناء إعادة التوليد</li>
                <li><i class="fas fa-check"></i> إشعارات تفاعلية للنجاح والأخطاء</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-cog"></i> كيفية العمل</h3>
            <div class="test-description">
                عند تمرير الماوس فوق رسالة البوت، ستظهر أزرار الإجراءات في الأسفل:
            </div>
            
            <!-- رسالة تجريبية -->
            <div class="demo-message user">
                مرحباً، اشرح لي كيفية عمل JavaScript
            </div>
            
            <div class="demo-message bot">
                <div class="message-content">
                    JavaScript هي لغة برمجة قوية تُستخدم لتطوير المواقع التفاعلية...
                </div>
                <div class="message-actions" style="opacity: 1;">
                    <button class="message-action-btn">
                        <i class="fas fa-redo"></i>
                        إعادة توليد
                    </button>
                    <button class="message-action-btn">
                        <i class="fas fa-copy"></i>
                        نسخ
                    </button>
                    <button class="message-action-btn">
                        <i class="fas fa-edit"></i>
                        تحرير
                    </button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-mobile-alt"></i> التجاوب مع الأجهزة</h3>
            <div class="test-description">
                تم تحسين التصميم ليعمل بشكل مثالي على جميع الأجهزة:
            </div>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> أزرار مرئية دائماً على الأجهزة المحمولة</li>
                <li><i class="fas fa-check"></i> أحجام مناسبة للشاشات الصغيرة</li>
                <li><i class="fas fa-check"></i> تخطيط مرن يتكيف مع العرض</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-play"></i> اختبار الميزات</h3>
            <div class="test-description">
                اختبر الميزات الجديدة:
            </div>

            <button class="test-button" onclick="window.open('index.html', '_blank')">
                <i class="fas fa-external-link-alt"></i>
                فتح التطبيق الرئيسي
            </button>

            <button class="test-button" onclick="testCopyFunction()">
                <i class="fas fa-copy"></i>
                اختبار النسخ
            </button>

            <button class="test-button" onclick="showRegenerateDemo()">
                <i class="fas fa-redo"></i>
                عرض توضيحي لإعادة التوليد
            </button>

            <div id="test-status"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-bug"></i> إصلاح المشكلة</h3>
            <div class="test-description">
                <strong>المشكلة المحلولة:</strong> كانت إعادة التوليد تحدث دائماً آخر رسالة بدلاً من الرسالة المحددة.
            </div>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> إضافة معرف فريد لكل رسالة</li>
                <li><i class="fas fa-check"></i> تتبع موضع الرسالة في المحادثة</li>
                <li><i class="fas fa-check"></i> تحديث الرسالة المحددة فقط في التخزين</li>
                <li><i class="fas fa-check"></i> الحفاظ على ترتيب الرسائل الصحيح</li>
            </ul>
            <div class="status info">
                <i class="fas fa-info-circle"></i>
                الآن كل رسالة لها معرف فريد وتتم إعادة توليدها بشكل مستقل!
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-code"></i> التحسينات التقنية</h3>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> استخدام Clipboard API الحديث مع fallback</li>
                <li><i class="fas fa-check"></i> تأثيرات CSS متقدمة مع animations</li>
                <li><i class="fas fa-check"></i> إدارة حالة محسنة للأزرار</li>
                <li><i class="fas fa-check"></i> معالجة أخطاء شاملة</li>
                <li><i class="fas fa-check"></i> تحديث تلقائي للمحادثات المحفوظة</li>
            </ul>
        </div>
    </div>

    <script>
        function testCopyFunction() {
            const testText = "هذا نص تجريبي لاختبار وظيفة النسخ!";
            
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(testText).then(() => {
                    showStatus("تم نسخ النص بنجاح باستخدام Clipboard API!", "success");
                }).catch(() => {
                    showStatus("فشل في النسخ باستخدام Clipboard API", "error");
                });
            } else {
                // استخدام الطريقة التقليدية
                const textArea = document.createElement('textarea');
                textArea.value = testText;
                document.body.appendChild(textArea);
                textArea.select();
                const success = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (success) {
                    showStatus("تم نسخ النص بنجاح باستخدام execCommand!", "success");
                } else {
                    showStatus("فشل في النسخ", "error");
                }
            }
        }
        
        function showRegenerateDemo() {
            showStatus("جاري إعادة التوليد... (محاكاة)", "info");
            
            setTimeout(() => {
                showStatus("تم إعادة توليد الرد بنجاح! (محاكاة)", "success");
            }, 2000);
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('test-status');
            statusDiv.innerHTML = `<div class="status ${type}"><i class="fas fa-${type === 'success' ? 'check' : type === 'info' ? 'info-circle' : 'exclamation-triangle'}"></i> ${message}</div>`;
            
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }
        
        // إضافة تأثير hover للرسالة التجريبية
        document.addEventListener('DOMContentLoaded', function() {
            const demoBot = document.querySelector('.demo-message.bot');
            if (demoBot) {
                demoBot.addEventListener('mouseenter', function() {
                    this.querySelector('.message-actions').style.opacity = '1';
                });
                demoBot.addEventListener('mouseleave', function() {
                    // على الأجهزة المحمولة، اتركها مرئية
                    if (window.innerWidth > 600) {
                        this.querySelector('.message-actions').style.opacity = '0.7';
                    }
                });
            }
        });
    </script>
</body>
</html>
