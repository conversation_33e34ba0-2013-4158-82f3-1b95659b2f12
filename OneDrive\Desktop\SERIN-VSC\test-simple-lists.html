<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القوائم البسيطة</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            background: var(--bg-dark);
            color: var(--text-light);
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: var(--bg-medium);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        .test-title {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار القوائم البسيطة المحسنة</h1>

        <div class="test-section">
            <div class="test-title">قائمة بسيطة</div>
            <div class="message bot">
                <p>قائمة بسيطة:</p>
                <ul>
                    <li>العنصر الأول</li>
                    <li>العنصر الثاني</li>
                    <li>العنصر الثالث</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">قائمة مرقمة</div>
            <div class="message bot">
                <p>قائمة مرقمة:</p>
                <ol>
                    <li>الخطوة الأولى</li>
                    <li>الخطوة الثانية</li>
                    <li>الخطوة الثالثة</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">قائمة متداخلة</div>
            <div class="message bot">
                <p>قائمة متداخلة:</p>
                <ul>
                    <li>العنصر الرئيسي الأول
                        <ul>
                            <li>عنصر فرعي أ</li>
                            <li>عنصر فرعي ب</li>
                        </ul>
                    </li>
                    <li>العنصر الرئيسي الثاني</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">قائمة مختلطة</div>
            <div class="message bot">
                <p>قائمة مختلطة:</p>
                <ol>
                    <li>عنصر مع فقرة
                        <p>هذه فقرة داخل عنصر القائمة</p>
                    </li>
                    <li>عنصر عادي</li>
                    <li>عنصر مع قائمة فرعية
                        <ul>
                            <li>فرعي 1</li>
                            <li>فرعي 2</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">قائمة في رسالة المستخدم</div>
            <div class="message user">
                <p>قائمة في رسالة المستخدم:</p>
                <ul>
                    <li>مهمة 1</li>
                    <li>مهمة 2</li>
                    <li>مهمة 3</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // تطبيق إصلاحات القوائم
        document.addEventListener('DOMContentLoaded', function() {
            fixNestedLists();
        });

        // محاكاة إضافة محتوى ديناميكي
        setTimeout(() => {
            const testContainer = document.querySelector('.test-container');
            const newSection = document.createElement('div');
            newSection.className = 'test-section';
            newSection.innerHTML = `
                <div class="test-title">قائمة مضافة ديناميكياً</div>
                <div class="message bot">
                    <p>قائمة مضافة ديناميكياً:</p>
                    <ul>
                        <li>عنصر ديناميكي 1</li>
                        <li>عنصر ديناميكي 2
                            <ol>
                                <li>فرعي مرقم 1</li>
                                <li>فرعي مرقم 2</li>
                            </ol>
                        </li>
                        <li>عنصر ديناميكي 3</li>
                    </ul>
                </div>
            `;
            testContainer.appendChild(newSection);

            // تطبيق الإصلاحات على المحتوى الجديد
            fixNestedLists();
        }, 2000);
    </script>
</body>
</html>
