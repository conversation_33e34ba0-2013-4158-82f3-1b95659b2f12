<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح القوائم</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            background: var(--bg-dark);
            color: var(--text-light);
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: var(--bg-medium);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        .test-title {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
        }
        .test-input {
            background: var(--bg-darker);
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 0.9em;
            border: 1px solid var(--border-color);
        }
        .test-output {
            background: var(--bg-light);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار إصلاح مشكلة العنصر الأول في القوائم</h1>
        
        <div class="test-section">
            <div class="test-title">اختبار القائمة المرقمة</div>
            <div class="test-input">
                النص الأصلي:<br>
                1. العنصر الأول<br>
                2. العنصر الثاني<br>
                3. العنصر الثالث
            </div>
            <div class="test-output">
                <div class="message bot" id="test1">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">اختبار القائمة النقطية</div>
            <div class="test-input">
                النص الأصلي:<br>
                - العنصر الأول<br>
                - العنصر الثاني<br>
                - العنصر الثالث
            </div>
            <div class="test-output">
                <div class="message bot" id="test2">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">اختبار القائمة المختلطة</div>
            <div class="test-input">
                النص الأصلي:<br>
                هذه قائمة مختلطة:<br>
                1. العنصر المرقم الأول<br>
                2. العنصر المرقم الثاني<br>
                <br>
                وهذه قائمة نقطية:<br>
                - النقطة الأولى<br>
                - النقطة الثانية
            </div>
            <div class="test-output">
                <div class="message bot" id="test3">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">اختبار القائمة مع نص قبلها وبعدها</div>
            <div class="test-input">
                النص الأصلي:<br>
                هذا نص قبل القائمة.<br>
                1. العنصر الأول<br>
                2. العنصر الثاني<br>
                3. العنصر الثالث<br>
                هذا نص بعد القائمة.
            </div>
            <div class="test-output">
                <div class="message bot" id="test4">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">اختبار آخر عنصر في القائمة</div>
            <div class="test-input">
                النص الأصلي:<br>
                1. العنصر الأول<br>
                2. العنصر الثاني<br>
                3. العنصر الثالث<br>
                4. العنصر الرابع<br>
                5. العنصر الخامس
            </div>
            <div class="test-output">
                <div class="message bot" id="test5">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">اختبار قائمة تنتهي بنص</div>
            <div class="test-input">
                النص الأصلي:<br>
                - العنصر الأول<br>
                - العنصر الثاني<br>
                - العنصر الثالث<br>
                والآن نص عادي بعد القائمة.
            </div>
            <div class="test-output">
                <div class="message bot" id="test6">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // اختبار دالة renderMarkdown مباشرة
        document.addEventListener('DOMContentLoaded', function() {
            // اختبار 1: قائمة مرقمة
            const test1Text = `1. العنصر الأول
2. العنصر الثاني
3. العنصر الثالث`;
            document.getElementById('test1').innerHTML = renderMarkdown(test1Text);

            // اختبار 2: قائمة نقطية
            const test2Text = `- العنصر الأول
- العنصر الثاني
- العنصر الثالث`;
            document.getElementById('test2').innerHTML = renderMarkdown(test2Text);

            // اختبار 3: قائمة مختلطة
            const test3Text = `هذه قائمة مختلطة:
1. العنصر المرقم الأول
2. العنصر المرقم الثاني

وهذه قائمة نقطية:
- النقطة الأولى
- النقطة الثانية`;
            document.getElementById('test3').innerHTML = renderMarkdown(test3Text);

            // اختبار 4: قائمة مع نص قبلها وبعدها
            const test4Text = `هذا نص قبل القائمة.
1. العنصر الأول
2. العنصر الثاني
3. العنصر الثالث
هذا نص بعد القائمة.`;
            document.getElementById('test4').innerHTML = renderMarkdown(test4Text);

            // اختبار 5: آخر عنصر في القائمة
            const test5Text = `1. العنصر الأول
2. العنصر الثاني
3. العنصر الثالث
4. العنصر الرابع
5. العنصر الخامس`;
            document.getElementById('test5').innerHTML = renderMarkdown(test5Text);

            // اختبار 6: قائمة تنتهي بنص
            const test6Text = `- العنصر الأول
- العنصر الثاني
- العنصر الثالث
والآن نص عادي بعد القائمة.`;
            document.getElementById('test6').innerHTML = renderMarkdown(test6Text);

            // تطبيق إصلاحات القوائم
            fixNestedLists();

            // طباعة النتائج للتشخيص
            console.log('Test 5 HTML:', document.getElementById('test5').innerHTML);
            console.log('Test 6 HTML:', document.getElementById('test6').innerHTML);
        });
    </script>
</body>
</html>
